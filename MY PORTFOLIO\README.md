# Tarik <PERSON>i Portfolio - Laravel Application

This is the portfolio website of <PERSON><PERSON>, a freelance Web and Software Developer, Video editor, Graphic Designer and social media manager with 4+ years of experience.

## About This Project

This portfolio has been transformed from a static HTML website into a full Laravel application while preserving all existing functionality and design. The transformation includes:

### Features
- **Responsive Design**: Fully responsive portfolio showcasing design work, videos, and skills
- **Contact Form**: Functional contact form with validation and database storage
- **Portfolio Gallery**: Interactive gallery with image and video popups
- **Modern Architecture**: Built with Laravel framework for scalability and maintainability
- **Asset Management**: Optimized asset loading with Lara<PERSON>'s asset helpers
- **Database Integration**: Contact form submissions stored in SQLite database

### Technologies Used
- **Backend**: Laravel 12.x
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap
- **Database**: SQLite
- **Assets**: jQuery, GSAP, Slick Slider, Magnific Popup
- **Icons**: Remix Icons

### Pages Included
- **Home**: Main portfolio showcase with recent designs and videos
- **About**: Personal information, experience, education, and services
- **Projects**: Complete portfolio gallery
- **Blog**: Blog section (ready for content)
- **Contact**: Contact form with validation

## Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd MY\ PORTFOLIO
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database setup**
   ```bash
   php artisan migrate
   ```

5. **Start the development server**
   ```bash
   php artisan serve
   ```

6. **Visit the application**
   Open your browser and go to `http://127.0.0.1:8000`

## Contact Information

- **Email**: <EMAIL>
- **Phone**: +212 690593056
- **Location**: Agadir, Ait Melloul, Morocco
- **Instagram**: [@bykirat](https://www.instagram.com/bykirat/)
- **LinkedIn**: [Tarik Anni](https://www.linkedin.com/in/tarik-anni-153700299)
- **GitHub**: [@annitarik0](https://github.com/annitarik0)

## Services Offered

- Social Media Management
- Graphic Design
- Video Editing
- Web and Software Development

## License

This project is proprietary and belongs to Tarik Anni. All rights reserved.
