// Create the modal element
var modal = document.createElement("div");
modal.className = "modal";
document.body.appendChild(modal);

// Create the video element
var video = document.createElement("video");
video.setAttribute("controls", "controls");
video.setAttribute("width", "600");
modal.appendChild(video);

// Create the close button
var span = document.createElement("span");
span.className = "close";
span.innerHTML = "&times;";
modal.appendChild(span);

// Get all video popup links
var videoLinks = document.querySelectorAll('.video_popup');

// When the user clicks on a video link, open the modal and play the video
videoLinks.forEach(function(videoLink) {
    videoLink.onclick = function(event) {
        event.preventDefault(); // Prevent the default anchor click behavior
        var videoSrc = this.getAttribute("href");
        video.innerHTML = `<source src="${videoSrc}" type="video/mp4">`;
        modal.style.display = "block";
        video.play();
    }
});

// When the user clicks on <span> (x), close the modal and pause the video
span.onclick = function() {
    modal.style.display = "none";
    video.pause();
    video.innerHTML = ""; // Clear the video source
}

// When the user clicks anywhere outside of the modal, close it and pause the video
window.onclick = function(event) {
    if (event.target == modal) {
        modal.style.display = "none";
        video.pause();
        video.innerHTML = ""; // Clear the video source
    }
}