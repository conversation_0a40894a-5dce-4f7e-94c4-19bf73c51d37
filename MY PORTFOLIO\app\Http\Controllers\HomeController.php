<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Portfolio;
use App\Models\Blog;

class HomeController extends Controller
{
    /**
     * Display the home page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get featured portfolio items for the homepage
        $featuredDesigns = Portfolio::published()
            ->featured()
            ->byType('image')
            ->ordered()
            ->limit(6)
            ->get();

        $featuredVideos = Portfolio::published()
            ->featured()
            ->byType('video')
            ->ordered()
            ->limit(5)
            ->get();

        // Get latest blog posts for homepage preview
        $latestBlogs = Blog::published()
            ->latest()
            ->limit(3)
            ->get();

        // Portfolio statistics
        $stats = [
            'years_experience' => 4,
            'completed_projects' => Portfolio::published()->count() ?: 50,
            'client_satisfactions' => 25,
            'cups_of_atay' => 100,
        ];

        // Software tools
        $tools = [
            [
                'name' => 'Premiere Pro',
                'image' => 'assets/images/projects/PREMIERE PRO.png'
            ],
            [
                'name' => 'Photoshop',
                'image' => 'assets/images/projects/PHOTOSHOP.png'
            ],
            [
                'name' => 'Illustrator',
                'image' => 'assets/images/projects/ILLUSTRATEUR.png'
            ],
            [
                'name' => 'After Effects',
                'image' => 'assets/images/projects/AFTER EFFECT.png'
            ],
        ];

        return view('home', compact(
            'featuredDesigns',
            'featuredVideos',
            'latestBlogs',
            'stats',
            'tools'
        ));
    }
}
