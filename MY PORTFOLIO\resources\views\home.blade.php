@extends('layouts.app')

@section('title', '<PERSON><PERSON>lio')
@section('description', '<PERSON><PERSON> - Freelance Web and Software Developer, Video editor, Graphic Designer and social media manager with 4+ years of experience')

@section('content')
<!-- START HEADER DESIGN AREA -->
<section id="home" class="main-hero-area">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-4">
                <div class="glitch">
                    <img src="{{ asset('assets/images/about/me.jpg') }}" alt="About Me" />
                    <div class="glitch__layers">
                        <div class="glitch__layer"></div>
                        <div class="glitch__layer"></div>
                        <div class="glitch__layer"></div>
                    </div>
                </div>
            </div>
            <div class="col-lg-8">
                <!-- START HERO DESIGN AREA -->
                <div class="hero-content wow fadeInUp delay-0-2s">
                    <h1>Hi there !</h1>
                    <h2>i'm <PERSON><PERSON> i'm a freelance Web and Software Developer , Video editor , Graphic Designer and social media manager with 4+ years of experience . </h2>
                    <div class="hero-btns">
                        <a href="{{ asset('assets/TARIK ANNI CV.pdf') }}" download class="theme-btn">Download CV <i class="ri-download-line"></i></a>
                    </div>
                </div>
                <!-- / END HERO DESIGN AREA -->
            </div>
        </div>
    </div>
</section>
<!-- / END HEADER DESIGN AREA -->

<!-- START PORTFOLIO DESIGN AREA -->
<div class="projects-area" id="portfolio">
    <div class="custom-icon" >
        <h1>Some of my recent Designs</h1>
    </div>
    <div class="container-fluid">
        <div class="row g-4 portfolio-grid">
            @forelse($featuredDesigns as $design)
                <div class="col-md-6 col-xl-6 portfolio-item category-{{ $loop->iteration }}">
                    <a href="{{ $design->display_url }}" class="{{ $design->popup_class }}">
                        <div class="portfolio-box">
                            <!-- Image -->
                            <img src="{{ $design->image_url }}" alt="{{ $design->title }}" data-rjs="2">
                            <!-- Category -->
                            <span class="portfolio-category">{{ $design->category }}</span>
                            <!-- Caption -->
                            <div class="portfolio-caption">
                                <h1>{{ $design->title }}</h1>
                            </div>
                        </div>
                    </a>
                </div>
            @empty
                <!-- Fallback to static content if no dynamic data -->
                <div class="col-md-6 col-xl-6 portfolio-item category-1">
                    <a href="{{ asset('assets/images/projects/work1.jpg') }}" class="work-popup">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/work1.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Presentations</span>
                            <div class="portfolio-caption">
                                <h1>Marketing Plan</h1>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-md-6 col-xl-6 portfolio-item category-2">
                    <a href="{{ route('single-project') }}">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/work2.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Posts</span>
                            <div class="portfolio-caption">
                                <h1>Instagram Post Design</h1>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-md-6 col-xl-4 portfolio-item category-2">
                    <a href="{{ asset('assets/images/projects/FERRARI.png') }}" class="work-popup">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/FERRARI THUMB.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Poster</span>
                            <div class="portfolio-caption">
                                <h1>Ferrari F40 Poster</h1>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-md-6 col-xl-4 portfolio-item category-1">
                    <a href="{{ route('slides-project') }}">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/work4.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Slides</span>
                            <div class="portfolio-caption">
                                <h1>Instagram Slides Design</h1>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-md-6 col-xl-4 portfolio-item category-2">
                    <a href="{{ asset('assets/images/projects/THUMBNAIL REEL 3.png') }}" class="work-popup">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/work5.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Thumbnails</span>
                            <div class="portfolio-caption">
                                <h1>Instagram Reel Thumbnail</h1>
                            </div>
                        </div>
                    </a>
                </div>
            @endforelse
        </div>
    </div>
</div>

<div class="projects-areea" id="video-portfolio">
    <div class="custom-icon">
        <h1>Some of my recent Videos</h1>
    </div>
    <div class="container-fluid">
        <div class="row g-4 portfolio-grid">
            @forelse($featuredVideos as $video)
                <div class="col-md-6 col-xl-{{ $loop->iteration <= 2 ? '6' : '4' }} portfolio-item category-{{ $loop->iteration }}">
                    <a href="{{ $video->video_url }}" type="video/mp4" class="video-popup">
                        <div class="portfolio-box">
                            <!-- Video -->
                            <img src="{{ $video->image_url }}" alt="{{ $video->title }}" data-rjs="2">
                            <!-- Category -->
                            <span class="portfolio-category">{{ $video->category }}</span>
                            <!-- Caption -->
                            <div class="portfolio-caption">
                                <h1>{{ $video->title }}</h1>
                            </div>
                        </div>
                    </a>
                </div>
            @empty
                <!-- Fallback to static content if no dynamic data -->
                <div class="col-md-6 col-xl-6 portfolio-item category-1">
                    <a href="{{ asset('assets/images/projects/REEL 2.1 IMAD.mp4') }}" type="video/mp4" class="video-popup">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/EDUCATIONAL REEL.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Instagram Reels</span>
                            <div class="portfolio-caption">
                                <h1>Educational Reels</h1>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-md-6 col-xl-6 portfolio-item category-2">
                    <a href="{{ asset('assets/images/projects/IMAD REEL 3.1 .mp4') }}" type="video/mp4" class="video-popup">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/THUMB REEL 3.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Instagram Reels</span>
                            <div class="portfolio-caption">
                                <h1>Educational Reels</h1>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-md-6 col-xl-4 portfolio-item category-2">
                    <a href="{{ asset('assets/images/projects/REEL1.1.mp4') }}" type="video/mp4" class="video-popup">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/work5.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Instagram Reels</span>
                            <div class="portfolio-caption">
                                <h1>Educational Reels</h1>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-md-6 col-xl-4 portfolio-item category-1">
                    <a href="{{ asset('assets/images/projects/IHAB_1.mp4') }}" type="video/mp4" class="video-popup">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/work5.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Ads Videos</span>
                            <div class="portfolio-caption">
                                <h1>Video Publicitaire</h1>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-md-6 col-xl-4 portfolio-item category-2">
                    <a href="{{ asset('assets/images/projects/IMAD REEL 5 RL.mp4') }}" type="video/mp4" class="video-popup">
                        <div class="portfolio-box">
                            <img src="{{ asset('assets/images/projects/work5.jpg') }}" alt="" data-rjs="2">
                            <span class="portfolio-category">Instagram Reels</span>
                            <div class="portfolio-caption">
                                <h1>RL Video</h1>
                            </div>
                        </div>
                    </a>
                </div>
            @endforelse
        </div>
    </div>
</div>
<!-- // END PORTFOLIO DESIGN AREA -->

<!-- START COMPANY DESIGN AREA -->
<div class="company-design-area">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <h2>Softwares I work with</h2>
                <div class="company-list">
                    <div class="scroller" data-direction="left" data-speed="slow">
                        <div class="scroller__inner">
                            @foreach($tools as $tool)
                                <img src="{{ asset($tool['image']) }}" width="50px" height="50px" alt="{{ $tool['name'] }}" title="{{ $tool['name'] }}" />
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- // END COMPANY DESIGN AREA -->

<!-- START CALL TO ACTION DESIGN AREA -->
<section class="call-to-action-area">
    <div class="container">
        <div class="row">
            <!-- START ABOUT TEXT DESIGN AREA -->
            <div class="col-lg-12">
                <div class="call-to-action-part wow fadeInUp delay-0-2s text-center">
                    <h2>Are You Ready to kickstart your project?</h2>
                    <p>Reach out and let's make it happen ✨. I'm also available for full-time or Part-time opportunities to push the boundaries of design and video editing and deliver exceptional work.</p>
                    <div class="hero-btns">
                        <a href="{{ route('contact') }}" class="theme-btn call-to-action-button">Let's Talk </a>
                    </div>
                </div>
            </div>
            <!-- / END ABOUT TEXT DESIGN AREA -->
        </div>
    </div>
</section>
<!--  // END CALL TO ACTION DESIGN AREA -->
@endsection
