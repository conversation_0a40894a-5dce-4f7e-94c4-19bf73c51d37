<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolios', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('category'); // presentations, posts, posters, slides, thumbnails, videos, etc.
            $table->string('type'); // image, video, external
            $table->string('image_path')->nullable();
            $table->string('video_path')->nullable();
            $table->string('external_url')->nullable();
            $table->json('tools_used')->nullable(); // array of tools like Photoshop, Premiere Pro, etc.
            $table->string('client')->nullable();
            $table->date('project_date')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->enum('status', ['draft', 'published'])->default('published');
            $table->timestamps();
            
            $table->index(['status', 'is_featured']);
            $table->index(['category', 'status']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolios');
    }
};
