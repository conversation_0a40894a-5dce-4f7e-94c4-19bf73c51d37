<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Portfolio extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'category',
        'type',
        'image_path',
        'video_path',
        'external_url',
        'tools_used',
        'client',
        'project_date',
        'is_featured',
        'sort_order',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'project_date' => 'date',
        'is_featured' => 'boolean',
        'tools_used' => 'array',
    ];

    /**
     * Scope a query to only include published portfolios.
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope a query to only include featured portfolios.
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeByCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query to filter by type.
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    /**
     * Get the portfolio's image URL.
     */
    public function getImageUrlAttribute(): string
    {
        return $this->image_path ? asset($this->image_path) : asset('assets/images/default-portfolio.jpg');
    }

    /**
     * Get the portfolio's video URL.
     */
    public function getVideoUrlAttribute(): ?string
    {
        return $this->video_path ? asset($this->video_path) : null;
    }

    /**
     * Get the portfolio's tools as a string.
     */
    public function getToolsStringAttribute(): string
    {
        return is_array($this->tools_used) ? implode(', ', $this->tools_used) : '';
    }

    /**
     * Check if the portfolio has a video.
     */
    public function hasVideo(): bool
    {
        return !empty($this->video_path);
    }

    /**
     * Check if the portfolio has an external URL.
     */
    public function hasExternalUrl(): bool
    {
        return !empty($this->external_url);
    }

    /**
     * Get the portfolio's display URL.
     */
    public function getDisplayUrlAttribute(): string
    {
        if ($this->hasExternalUrl()) {
            return $this->external_url;
        }
        
        if ($this->hasVideo()) {
            return $this->video_url;
        }
        
        return $this->image_url;
    }

    /**
     * Get the portfolio's popup class.
     */
    public function getPopupClassAttribute(): string
    {
        if ($this->hasVideo()) {
            return 'video-popup';
        }
        
        if ($this->hasExternalUrl()) {
            return '';
        }
        
        return 'work-popup';
    }
}
