{"name": "illuminate/collections", "description": "The Illuminate Collections package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/conditionable": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0"}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["functions.php", "helpers.php"]}, "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "suggest": {"illuminate/http": "Required to convert collections to API resources (^12.0).", "symfony/var-dumper": "Required to use the dump method (^7.2)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}