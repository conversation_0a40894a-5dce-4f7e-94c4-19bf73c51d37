/* Modal styles */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0, 0, 0, 0.5); /* Black with 50% opacity */
}

/* Video styles */
.modal video {
    margin: auto;
    display: block;
    max-width: 80%; /* Set max width to 80% of the modal */
    max-height: 80%; /* Set max height to 80% of the modal */
    position: relative;
    top: 50%;
    transform: translateY(-50%); /* Center vertically */
}

/* Close button styles */
.close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}