<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Blog;

class BlogController extends Controller
{
    /**
     * Display the blog page.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $query = Blog::published()->latest();

        // Filter by tag if provided
        if ($request->has('tag') && $request->tag) {
            $query->byTag($request->tag);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%")
                  ->orWhere('excerpt', 'like', "%{$searchTerm}%");
            });
        }

        $blogs = $query->paginate(9);

        // Get all unique tags for filter
        $allTags = Blog::published()
            ->whereNotNull('tags')
            ->get()
            ->pluck('tags')
            ->flatten()
            ->unique()
            ->sort()
            ->values();

        // Get recent posts for sidebar
        $recentPosts = Blog::published()
            ->latest()
            ->limit(5)
            ->get();

        return view('blog', compact('blogs', 'allTags', 'recentPosts'));
    }

    /**
     * Display a single blog post.
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $blog = Blog::published()
            ->where('slug', $slug)
            ->firstOrFail();

        // Get related posts
        $relatedPosts = Blog::published()
            ->where('id', '!=', $blog->id)
            ->latest()
            ->limit(3)
            ->get();

        // Get previous and next posts
        $previousPost = Blog::published()
            ->where('published_at', '<', $blog->published_at)
            ->latest()
            ->first();

        $nextPost = Blog::published()
            ->where('published_at', '>', $blog->published_at)
            ->oldest()
            ->first();

        return view('single-blog', compact('blog', 'relatedPosts', 'previousPost', 'nextPost'));
    }

    /**
     * Display a single blog post (fallback for old route).
     *
     * @param string|null $slug
     * @return \Illuminate\View\View
     */
    public function single($slug = null)
    {
        if ($slug) {
            return $this->show($slug);
        }

        // Default blog post for demonstration
        $blog = (object) [
            'id' => 1,
            'title' => 'Welcome to My Blog',
            'slug' => 'welcome-to-my-blog',
            'content' => 'This blog section is currently under development. I\'m working on creating valuable content that will help you learn more about design, development, and creative processes.',
            'excerpt' => 'Welcome to my blog where I share insights about design and development.',
            'featured_image' => 'assets/images/blog/blog-post.jpg',
            'published_at' => now(),
            'reading_time' => 2,
            'tags' => ['welcome', 'introduction'],
        ];

        $relatedPosts = collect();
        $previousPost = null;
        $nextPost = null;

        return view('single-blog', compact('blog', 'relatedPosts', 'previousPost', 'nextPost'));
    }

    /**
     * Get posts by tag (AJAX endpoint).
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByTag(Request $request)
    {
        $tag = $request->get('tag');

        $blogs = Blog::published()
            ->byTag($tag)
            ->latest()
            ->paginate(9);

        return response()->json([
            'success' => true,
            'blogs' => $blogs,
            'tag' => $tag,
        ]);
    }

    /**
     * Search posts (AJAX endpoint).
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $searchTerm = $request->get('q');

        $blogs = Blog::published()
            ->where(function ($query) use ($searchTerm) {
                $query->where('title', 'like', "%{$searchTerm}%")
                      ->orWhere('content', 'like', "%{$searchTerm}%")
                      ->orWhere('excerpt', 'like', "%{$searchTerm}%");
            })
            ->latest()
            ->paginate(9);

        return response()->json([
            'success' => true,
            'blogs' => $blogs,
            'search_term' => $searchTerm,
            'count' => $blogs->total(),
        ]);
    }
}
