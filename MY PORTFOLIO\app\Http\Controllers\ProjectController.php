<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Portfolio;

class ProjectController extends Controller
{
    /**
     * Display the projects page.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $query = Portfolio::published()->ordered();

        // Filter by category if provided
        if ($request->has('category') && $request->category !== 'all') {
            $query->byCategory($request->category);
        }

        // Filter by type if provided
        if ($request->has('type') && $request->type !== 'all') {
            $query->byType($request->type);
        }

        $portfolios = $query->get();

        // Get unique categories for filter
        $categories = Portfolio::published()
            ->select('category')
            ->distinct()
            ->pluck('category')
            ->sort();

        // Get unique types for filter
        $types = Portfolio::published()
            ->select('type')
            ->distinct()
            ->pluck('type')
            ->sort();

        return view('projects', compact('portfolios', 'categories', 'types'));
    }

    /**
     * Display a single project page.
     *
     * @param \App\Models\Portfolio $portfolio
     * @return \Illuminate\View\View
     */
    public function show(Portfolio $portfolio)
    {
        // Get related projects
        $relatedProjects = Portfolio::published()
            ->where('id', '!=', $portfolio->id)
            ->byCategory($portfolio->category)
            ->ordered()
            ->limit(3)
            ->get();

        return view('single-project', compact('portfolio', 'relatedProjects'));
    }

    /**
     * Display a single project by slug or default single project.
     *
     * @param string|null $slug
     * @return \Illuminate\View\View
     */
    public function single($slug = null)
    {
        if ($slug) {
            $portfolio = Portfolio::published()
                ->where('title', 'like', '%' . str_replace('-', ' ', $slug) . '%')
                ->firstOrFail();

            return $this->show($portfolio);
        }

        // Default single project (Instagram Post Design)
        $portfolio = (object) [
            'id' => 1,
            'title' => 'Instagram Post Design',
            'description' => 'A creative and engaging Instagram post design that captures attention and drives engagement.',
            'category' => 'Social Media Design',
            'type' => 'image',
            'image_path' => 'assets/images/projects/work2.jpg',
            'tools_used' => ['Photoshop', 'Illustrator'],
            'client' => 'Personal Project',
            'project_date' => '2024',
        ];

        return view('single-project', compact('portfolio'));
    }

    /**
     * Display the slides project page.
     *
     * @return \Illuminate\View\View
     */
    public function slides()
    {
        // Default slides project
        $portfolio = (object) [
            'id' => 2,
            'title' => 'Instagram Slides Design',
            'description' => 'A series of engaging Instagram slides designed to educate and inform the audience.',
            'category' => 'Social Media Design',
            'type' => 'image',
            'image_path' => 'assets/images/projects/work4.jpg',
            'tools_used' => ['Photoshop', 'Illustrator'],
            'client' => 'Educational Content',
            'project_date' => '2024',
            'slides_count' => 10,
        ];

        return view('slides-project', compact('portfolio'));
    }

    /**
     * Get projects by category (AJAX endpoint).
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByCategory(Request $request)
    {
        $category = $request->get('category', 'all');

        $query = Portfolio::published()->ordered();

        if ($category !== 'all') {
            $query->byCategory($category);
        }

        $portfolios = $query->get();

        return response()->json([
            'success' => true,
            'portfolios' => $portfolios,
            'count' => $portfolios->count(),
        ]);
    }
}
