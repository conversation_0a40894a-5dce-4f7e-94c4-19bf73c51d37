<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Portfolio;

class AboutController extends Controller
{
    /**
     * Display the about page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Personal information
        $personalInfo = [
            'name' => '<PERSON><PERSON> Anni',
            'title' => 'Innovative Developer and Creative Visual Artist',
            'description' => 'Hi, I\'m <PERSON><PERSON>, a dedicated web and software development student and a professional self-taught designer and video editor. With expertise in tools like Premiere Pro, Illustrator, After Effects, and Photoshop, I bring creativity to life across diverse styles and mediums.',
            'extended_description' => 'My passion lies in blending technical development skills with striking visual designs to craft unique and engaging digital experiences. Whether it\'s coding intuitive applications or producing visually compelling content, I\'m committed to pushing boundaries and delivering high-quality results.',
            'image' => 'assets/images/about/me.jpg',
        ];

        // Statistics
        $stats = [
            'years_experience' => 4,
            'completed_projects' => Portfolio::published()->count() ?: 50,
            'client_satisfactions' => 25,
            'cups_of_atay' => 100,
        ];

        // Experience and Education
        $experiences = [
            [
                'period' => '2024 - Present',
                'title' => 'Web and Software Development Student - Digital Marketing Internship at RANKUP',
                'subtitle' => '@ Web sites, software applications, and digital marketing campaigns',
                'subtitle2' => '@ Graphic Design, Video editing and Motion Graphic Freelancer',
                'description' => 'bachelor\'s student in web and software development currently interning at RankUp, a digital marketing agency in Agadir. Alongside my internship, I work as a freelance graphic designer and video editor, creating visually stunning designs and dynamic video content. My goal is to merge my development expertise with creative visuals to deliver impactful digital solutions.',
            ],
            [
                'period' => '2021 - 2023',
                'title' => 'Specialized Technician in Networks and Systems',
                'subtitle' => '@ Products Designer',
                'description' => 'I hold a diploma as a specialized technician in networks and computer systems, which has provided me with a strong foundation in IT infrastructure and problem-solving. This technical expertise complements my passion for web and software development, enabling me to create robust and efficient digital solutions.',
            ],
            [
                'period' => '2021',
                'title' => 'High School Diploma',
                'description' => 'I graduated from high school in Morocco with a Baccalaureate in Physics, which sharpened my analytical and problem-solving skills. This academic foundation has been instrumental in shaping my logical approach to both technical and creative challenges in my career.',
            ],
        ];

        // Services
        $services = [
            [
                'icon' => 'ri-global-fill',
                'title' => 'Social Media Management',
                'description' => 'As a social media manager with design and video editing skills, I create engaging content, manage platforms, and craft visuals and videos that boost brand presence and engagement.',
            ],
            [
                'icon' => 'ri-quill-pen-line',
                'title' => 'Graphic Design',
                'description' => 'As a graphic designer, I create eye-catching thumbnails for YouTube and Instagram, engaging posts, and visuals for diverse platforms, tailored to captivate audiences and boost brands.',
            ],
            [
                'icon' => 'ri-video-add-fill',
                'title' => 'Video Editing',
                'description' => 'I specialize in editing videos for ads, reels, YouTube, and other formats, delivering engaging and visually captivating content tailored to diverse audiences and platforms.',
            ],
            [
                'icon' => 'ri-star-line',
                'title' => 'Web and Software Development',
                'description' => 'Currently developing skills in modern web technologies and software development, focusing on creating efficient, scalable, and user-friendly applications.',
            ],
        ];

        // Skills and tools
        $skills = [
            'Design' => ['Photoshop', 'Illustrator', 'After Effects', 'Figma'],
            'Video' => ['Premiere Pro', 'After Effects', 'DaVinci Resolve'],
            'Development' => ['HTML/CSS', 'JavaScript', 'PHP', 'Laravel', 'React'],
            'Tools' => ['Git', 'VS Code', 'Adobe Creative Suite'],
        ];

        return view('about', compact(
            'personalInfo',
            'stats',
            'experiences',
            'services',
            'skills'
        ));
    }

    /**
     * Download CV file.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadCV()
    {
        $filePath = public_path('assets/TARIK ANNI CV.pdf');

        if (file_exists($filePath)) {
            return response()->download($filePath, 'Tarik_Anni_CV.pdf');
        }

        abort(404, 'CV file not found.');
    }

    /**
     * Get skills data (AJAX endpoint).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSkills()
    {
        $skills = [
            'Design' => ['Photoshop', 'Illustrator', 'After Effects', 'Figma'],
            'Video' => ['Premiere Pro', 'After Effects', 'DaVinci Resolve'],
            'Development' => ['HTML/CSS', 'JavaScript', 'PHP', 'Laravel', 'React'],
            'Tools' => ['Git', 'VS Code', 'Adobe Creative Suite'],
        ];

        return response()->json([
            'success' => true,
            'skills' => $skills,
        ]);
    }
}
