<!DOCTYPE html>
<html lang="en">

<head>
    <!-- META -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Wize - Creative Portfolio Showcase Template">
    <meta name="keywords" content="personal, portfolio new, html, one page, bootstrap, new html template, design, creative, onepage, clean, modern">
    <meta name="author" content="Tanvir Hossain">
    <!-- PAGE TITLE -->
    <title>Wize - Creative Personal Portfolio</title>
    <!-- FAVICON ICON -->
    <link rel="shortcut icon" href="assets/images/favicon.png" type="image/x-icon" />
    <!-- ALL GOOGLE FONTS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap" rel="stylesheet">
    <!-- BOOTSTRAP CSS -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css" />
    <!-- FONT AWESOME CSS -->
    <link rel="stylesheet" href="assets/fonts/remixicon.css" />
    <!-- MAGNIFIC CSS -->
    <link rel="stylesheet" href="assets/css/magnific-popup.css">
    <!-- NICE SELECT CSS -->
    <link rel="stylesheet" href="assets/css/nice-select.min.css" />
    <!-- ANIMATE CSS -->
    <link rel="stylesheet" href="assets/css/animate.min.css" />
    <!-- SLICK CSS -->
    <link rel="stylesheet" href="assets/css/slick.min.css" />
    <!-- SPACING CSS -->
    <link rel="stylesheet" href="assets/css/spacing.css" />
    <!-- MAIN STYLE CSS -->
    <link rel="stylesheet" href="assets/css/style.css" />
    <!-- RESPONSIVE CSS -->
    <link rel="stylesheet" href="assets/css/responsive.css">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>
    <!-- START PRELOADER AREA-->
    <div class="preloader">
        <svg viewbox="0 0 1000 1000" preserveaspectratio="none">
            <path id="preloaderSvg" d="M0,1005S175,995,500,995s500,5,500,5V0H0Z"></path>
        </svg>
        <div class="preloader-heading">
            <div class="load-text">
                <span>t</span>
                <span>a</span>
                <span>r</span>
                <span>i</span>
                <span>k</span>
                <span>&nbsp;</span>
                <span>a</span>
                <span>n</span>
                <span>n</span>
                <span>i</span>
            </div>
        </div>
    </div>
    <!-- END PRELOADER AREA -->
    <!-- START MAGIC CURSOR AND BALL AREA-->
    <div id="magic-cursor">
        <div id="ball"></div>
    </div>
    <div id="magic-cursor">
        <div id="ball"></div>
    </div>
    <!-- / END MAGIC CURSOR AND BALL AREA-->
    <!-- START MENU DESIGN AREA -->
    <header class="main-header">
        <div class="header-upper">
            <div class="container">
                <div class="header-inner d-flex align-items-center">
                    <!-- START LOGO DESIGN AREA -->
                    <div class="logo-outer">
                        <div class="logo">
                            <a href="index.html"><img src="assets/images/logos/logo.png" alt="Logo" title="Logo" /></a>
                        </div>
                    </div>
                    <!-- / END LOGO DESIGN AREA -->
                    <!-- START NAV DESIGN AREA -->
                    <div class="nav-outer clearfix mx-auto">
                        <!-- Main Menu -->
                        <nav class="main-menu navbar-expand-lg">
                            <div class="navbar-header">
                                <div class="mobile-logo">
                                    <a href="index.html">
                                        <img src="assets/images/logos/logo.png" alt="Logo" title="Logo" />
                                    </a>
                                </div>
                                <!-- Toggle Button -->
                                <button type="button" class="navbar-toggle" data-bs-toggle="collapse" data-bs-target=".navbar-collapse">
                                    <span class="icon-bar"></span>
                                    <span class="icon-bar"></span>
                                    <span class="icon-bar"></span>
                                </button>
                            </div>
                            <div class="navbar-collapse collapse">
                                <ul class="navigation clearfix">
                                    <li><a class="nav-link-click" href="index.html">Home</a></li>
                                    <li><a class="nav-link-click" href="about.html">about</a></li>
                                    <li><a class="nav-link-click" href="projects.html">Projects</a></li>
                                    <li><a class="nav-link-click" href="blog.html">blog</a></li>
                                    <li><a class="nav-link-click" href="contact.html">Contact</a></li>
                                </ul>
                            </div>
                        </nav>
                        <!-- / END NAV DESIGN AREA -->
                    </div>
                    <div class="about-social text-center">
                        <ul>
                            <li><a href="https://www.instagram.com/bykirat/"><i class="ri-instagram-fill"></i></a></li>
                            <li><a href="http://wa.me/212690593056"><i class="ri-whatsapp-fill"></i></a></li>
                            <li><a href="https://www.linkedin.com/in/tarik-anni-153700299?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app"><i class="ri-linkedin-fill"></i></a></li>
                            <li><a href="https://github.com/annitarik0"><i class="ri-github-line"></i></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- / END MENU DESIGN AREA -->
    <!-- START MENU DESIGN AREA -->
    <div id="smooth-wrapper">
        <div id="smooth-content">
            <main class="main-bg o-hidden">
                <!-- START HEADER DESIGN AREA -->
                <section class="single-page-hero-area">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-lg-12">
                                <h2>About me</h2>
                                <p>With over 4 years of dedicated focus on designing and editing videos that achieve business and educational goals, I have established myself as a trusted professional in the industry.</p>
                            </div>
                        </div>
                        <div class="row">
                            <!-- START COUNTER DESIGN AREA -->
                            <div class="col-6 col-lg-3">
                                <div class="hero-counter-area wow fadeInUp delay-0-4s">
                                    <div class="counter-item counter-text-wrap">
                                        <span class="count-text plus" data-speed="3000" data-stop="4">0</span>
                                        <span class="counter-title">Years Of Experience</span>
                                    </div>
                                </div>
                            </div>
                            <!-- / END COUNTER DESIGN AREA -->
                            <!-- START COUNTER DESIGN AREA -->
                            <div class="col-6 col-lg-3">
                                <div class="hero-counter-area wow fadeInUp delay-0-4s">
                                    <div class="counter-item counter-text-wrap">
                                        <span class="count-text plus" data-speed="3000" data-stop="50">0</span>
                                        <span class="counter-title">Complete Projects</span>
                                    </div>
                                </div>
                            </div>
                            <!-- / END COUNTER DESIGN AREA -->
                            <!-- START COUNTER DESIGN AREA -->
                            <div class="col-6 col-lg-3">
                                <div class="hero-counter-area wow fadeInUp delay-0-4s">
                                    <div class="counter-item counter-text-wrap">
                                        <span class="count-text plus" data-speed="3000" data-stop="25">0</span>
                                        <span class="counter-title">Client Satisfactions</span>
                                    </div>
                                </div>
                            </div>
                            <!-- / END COUNTER DESIGN AREA -->
                            <!-- START COUNTER DESIGN AREA -->
                            <div class="col-6 col-lg-3">
                                <div class="hero-counter-area wow fadeInUp delay-0-4s">
                                    <div class="counter-item counter-text-wrap">
                                        <span class="count-text plus" data-speed="3000" data-stop="100">0</span>
                                        <span class="counter-title">Cup of ATAY</span>
                                    </div>
                                </div>
                            </div>
                            <!-- / END COUNTER DESIGN AREA -->
                        </div>
                    </div>
                </section>
                <!-- / END HEADER DESIGN AREA -->
                <!-- START ABOUT DESIGN AREA -->
                <section id="about" class="about-area">
                    <div class="container">
                        <div class="container-inner">
                            <div class="row align-items-center">
                                <!-- START ABOUT IMAGE DESIGN AREA -->
                                <div class="col-lg-5">
                                    <div class="about-image-part wow fadeInUp delay-0-3s">
                                        <img src="assets/images/about/me.jpg" alt="About Me" />
                                    </div>
                                </div>
                                <!-- / END ABOUT IMAGE DESIGN AREA -->
                                <!-- START ABOUT TEXT DESIGN AREA -->
                                <div class="col-lg-7">
                                    <div class="about-content-part wow fadeInUp delay-0-5s">
                                        <h2>
                                            <span>Innovative Developer</span> and <span>Creative Visual Artist</span> 
                                        </h2>
                                        <p>
                                            Hi, I’m Tarik Anni, a dedicated web and software development student and a professional self-taught designer and video editor. With expertise in tools like Premiere Pro, Illustrator, After Effects, and Photoshop, I bring creativity to life across diverse styles and mediums.
                                        </p>
                                        <p>My passion lies in blending technical development skills with striking visual designs to craft unique and engaging digital experiences. Whether it’s coding intuitive applications or producing visually compelling content, I’m committed to pushing boundaries and delivering high-quality results.</p>
                                    </div>
                                </div>
                                <!-- / END ABOUT TEXT DESIGN AREA -->
                            </div>
                        </div>
                    </div>
                </section>
                <!-- / END ABOUT DESIGN AREA -->
                <!-- START RESUME EXPERIENCE DESIGN AREA -->
                <div class="resume-area" id="resume">
                    <div class="container">
                        <div class="container-inner">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12">
                                    <div class="section-title mb-40 wow fadeInUp delay-0-2s">
                                        <h2>Education & Experience</h2>
                                        <p>Established history of success in design , development and video Editing, consistently delivering valuable insights and driving significant results.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xl-10">
                                    <div class="resume-wrapper wow fadeInUp delay-0-2s">
                                        <!-- START SINGLE EXPERIENCE DESIGN AREA -->
                                        <div class="resume-box">
                                            <span class="resume-date">2024 - Present</span>
                                            <h5 class="fw-medium">Web and Software Development Student - Digital Marketing Internship at RANKUP</h5>
                                            <span>@ Web sites , software applications, and digital marketing campaigns</span><br>
                                            <span>@ Graphic Design , Video editing and Motion Graphic Freelancer</span>
                                            <p>bachelor’s student in web and software development currently interning at RankUp, a digital marketing agency in Agadir. Alongside my internship, I work as a freelance graphic designer and video editor, creating visually stunning designs and dynamic video content. My goal is to merge my development expertise with creative visuals to deliver impactful digital solutions.</p>
                                        </div>
                                        <!-- / END SINGLE EXPERIENCE DESIGN AREA -->
                                        <!-- START SINGLE EXPERIENCE DESIGN AREA -->
                                        <div class="resume-box">
                                            <span class="resume-date">2021 - 2023</span>
                                            <h5 class="fw-medium">Specialized Technician in Networks and Systems</h5>
                                            <span>@ Products Designer</span>
                                            <p>I hold a diploma as a specialized technician in networks and computer systems, which has provided me with a strong foundation in IT infrastructure and problem-solving. This technical expertise complements my passion for web and software development, enabling me to create robust and efficient digital solutions.</p>
                                        </div>
                                        <!-- / END SINGLE EXPERIENCE DESIGN AREA -->
                                        <!-- START SINGLE EXPERIENCE DESIGN AREA -->
                                        <div class="resume-box">
                                            <span class="resume-date">2021</span>
                                            <h5 class="fw-medium">High School Diploma</h5>
                                            <p>I graduated from high school in Morocco with a Baccalaureate in Physics, which sharpened my analytical and problem-solving skills. This academic foundation has been instrumental in shaping my logical approach to both technical and creative challenges in my career.</p>
                                        </div>
                                        <!-- / END SINGLE EXPERIENCE DESIGN AREA -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- / END RESUME EXPERIENCE DESIGN AREA -->
                <!-- START SERVICE DESIGN AREA -->
                <section id="services" class="services-area">
                    <div class="container">
                        <div class="container-inner">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12">
                                    <div class="section-title mb-40 wow fadeInUp delay-0-2s">
                                        <h2>Services</h2>
                                        <p>My Services Pave the Way for Exceptional Experiences, Where Quality and Commitment Define Every Interaction."</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <!-- START SINGLE SERVICE DESIGN AREA -->
                                <div class="col-lg-4 col-md-4">
                                    <div class="service-item wow fadeInUp delay-0-2s">
                                        <div class="content">
                                            <i class="ri-global-fill"></i>
                                            <h4>Socail Media Management</h4>
                                            <p>As a social media manager with design and video editing skills, I create engaging content, manage platforms, and craft visuals and videos that boost brand presence and engagement.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- / END SINGLE SERVICE DESIGN AREA -->
                                <!-- START SINGLE SERVICE DESIGN AREA -->
                                <div class="col-lg-4 col-md-4">
                                    <div class="service-item wow fadeInUp delay-0-4s">
                                        <div class="content">
                                            <i class="ri-quill-pen-line"></i>
                                            <h4>Graphic Design</h4>
                                            <p>As a graphic designer, I create eye-catching thumbnails for YouTube and Instagram, engaging posts, and visuals for diverse platforms, tailored to captivate audiences and boost brands.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- / END SINGLE SERVICE DESIGN AREA -->
                                <!-- START SINGLE SERVICE DESIGN AREA -->
                                <div class="col-lg-4 col-md-4">
                                    <div class="service-item wow fadeInUp delay-0-6s">
                                        <div class="content">
                                            <i class="ri-video-add-fill"></i>
                                            <h4>Video Editing</h4>
                                            <p>I specialize in editing videos for ads, reels, YouTube, and other formats, delivering engaging and visually captivating content tailored to diverse audiences and platforms.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-4">
                                    <div class="t9ad">
                                        <div class="service-item wow fadeInUp delay-0-8s">
                                            <div class="content">
                                                <i class="ri-star-line"></i>
                                                <h4>Web and Software Development</h4>
                                                <p>Loading ...</p>
                                            </div>
                                        </div>
                                    </div>    
                                </div>
                                <!-- / END SINGLE SERVICE DESIGN AREA -->
                            </div>
                        </div>
                    </div>
                </section>
                <!-- / END SERVICE DESIGN AREA -->
                <!-- START CALL TO ACTION DESIGN AREA -->
                <section class="call-to-action-area">
                    <div class="container">
                        <div class="row">
                            <!-- START ABOUT TEXT DESIGN AREA -->
                            <div class="col-lg-12">
                                <div class="call-to-action-part wow fadeInUp delay-0-2s text-center">
                                    <h2>Are You Ready to kickstart your project?</h2>
                                    <p>Reach out and let's make it happen ✨. I'm also available for full-time or Part-time opportunities to push the boundaries of design and deliver exceptional work.</p>
                                    <div class="hero-btns">
                                        <a href="contact.html" class="theme-btn call-to-action-button">Let's Talk <i class=""></i></a>
                                    </div>
                                </div>
                            </div>
                            <!-- / END ABOUT TEXT DESIGN AREA -->
                        </div>
                    </div>
                </section>
                <!--  // END CALL TO ACTION DESIGN AREA -->
                <!-- START FOOTER DESIGN AREA -->
                <footer class="main-footer">
                    <div class="footer-bottom pt-50 pb-40">
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-12 text-center">
                                    <div class="copyright-text">
                                        <p>
                                            &copy;Copyright <a href="index.html">ANNI</a> All
                                            Rights Reserved.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </footer>
            </main>
        </div>
    </div>
    <!-- / END FOOTER DESIGN AREA -->
    <!-- START SCROOL UP DESIGN AREA -->
    <div class="progress-wrap cursor-pointer">
        <i class="ri-arrow-up-s-line"></i>
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" />
        </svg>
    </div>
    <!-- / END SCROOL UP DESIGN AREA -->
    <!-- JQUERY JS -->
    <script src="assets/js/jquery-3.6.0.min.js"></script>
    <!-- BOOTSTRAP JS-->
    <script src="assets/js/bootstrap.min.js"></script>
    <!-- APPEAR JS -->
    <script src="assets/js/appear.min.js"></script>
    <!-- MAGNIFICANT JS -->
    <script src="assets/js/jquery.magnific-popup.min.js"></script>
    <!-- SLICK JS-->
    <script src="assets/js/slick.min.js"></script>
    <!-- GSAP AND LOCOMOTIV JS-->
    <script src="assets/js/gsap.min.js"></script>
    <script src="assets/js/ScrollSmoother.min.js"></script>
    <script src="assets/js/ScrollTrigger.min.js"></script>
    <script src="assets/js/smoother-script.js"></script>
    <!-- NICE SELECT JS-->
    <script src="assets/js/jquery.nice-select.min.js"></script>
    <!-- IMAGE LOADER JS-->
    <script src="assets/js/imagesloaded.pkgd.min.js"></script>
    <!-- ISOTOPE JS-->
    <script src="assets/js/isotope.pkgd.min.js"></script>
    <!--  WOW ANIMATION JS-->
    <script src="assets/js/wow.min.js"></script>
    <!-- SCRIPT JS-->
    <script src="assets/js/script.js"></script>
</body>

</html>