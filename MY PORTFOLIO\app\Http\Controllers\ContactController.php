<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Contact;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\ContactRequest;

class ContactController extends Controller
{
    /**
     * Display the contact page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Contact information
        $contactInfo = [
            'office' => 'Agadir, Ait Melloul',
            'phone' => '+212 690593056',
            'email' => '<EMAIL>',
            'social_links' => [
                'instagram' => 'https://www.instagram.com/bykirat/',
                'whatsapp' => 'http://wa.me/212690593056',
                'linkedin' => 'https://www.linkedin.com/in/tarik-anni-153700299?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app',
                'github' => 'https://github.com/annitarik0',
            ],
        ];

        // Get recent contact statistics (optional)
        $stats = [
            'total_messages' => Contact::count(),
            'unread_messages' => Contact::unread()->count(),
            'response_rate' => '100%',
            'avg_response_time' => '24 hours',
        ];

        return view('contact', compact('contactInfo', 'stats'));
    }

    /**
     * Store a contact form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Validate the form data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|min:2',
            'email' => 'required|email|max:255',
            'message' => 'required|string|max:2000|min:10',
        ], [
            'name.required' => 'Please enter your full name.',
            'name.min' => 'Name must be at least 2 characters.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'message.required' => 'Please enter your message.',
            'message.min' => 'Message must be at least 10 characters.',
            'message.max' => 'Message cannot exceed 2000 characters.',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Store the contact submission in the database
            $contact = Contact::create([
                'name' => $request->name,
                'email' => $request->email,
                'message' => $request->message,
                'status' => 'unread',
            ]);

            // Log the contact submission
            Log::info('New contact form submission', [
                'contact_id' => $contact->id,
                'name' => $contact->name,
                'email' => $contact->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Send email notification (implement if needed)
            $this->sendNotificationEmail($contact);

            $successMessage = 'Thank you for your message, ' . $request->name . '! I will get back to you within 24 hours.';

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $successMessage,
                    'contact_id' => $contact->id,
                ]);
            }

            return redirect()->back()->with('success', $successMessage);

        } catch (\Exception $e) {
            Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->only(['name', 'email']),
                'ip' => $request->ip(),
            ]);

            $errorMessage = 'Sorry, there was an error sending your message. Please try again or contact me <NAME_EMAIL>.';

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $errorMessage,
                ], 500);
            }

            return redirect()->back()
                ->with('error', $errorMessage)
                ->withInput();
        }
    }

    /**
     * Send notification email (implement as needed).
     *
     * @param \App\Models\Contact $contact
     * @return void
     */
    private function sendNotificationEmail(Contact $contact)
    {
        // Implement email sending logic here
        // You can use Laravel's Mail facade to send emails
        // For now, we'll just log it
        Log::info('Contact notification email would be sent', [
            'contact_id' => $contact->id,
            'to' => '<EMAIL>',
        ]);
    }

    /**
     * Get contact statistics (for admin use).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        $stats = [
            'total' => Contact::count(),
            'unread' => Contact::unread()->count(),
            'read' => Contact::read()->count(),
            'replied' => Contact::where('status', 'replied')->count(),
            'recent' => Contact::recent()->limit(5)->get(),
            'today' => Contact::whereDate('created_at', today())->count(),
            'this_week' => Contact::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month' => Contact::whereMonth('created_at', now()->month)->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Mark contact as read.
     *
     * @param \App\Models\Contact $contact
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Contact $contact)
    {
        $contact->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'Contact marked as read.',
        ]);
    }

    /**
     * Mark contact as replied.
     *
     * @param \App\Models\Contact $contact
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsReplied(Contact $contact)
    {
        $contact->markAsReplied();

        return response()->json([
            'success' => true,
            'message' => 'Contact marked as replied.',
        ]);
    }
}
