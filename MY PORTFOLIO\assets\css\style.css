﻿/*
* ----------------------------------------------------------------------------------------
Author       : <PERSON><PERSON>
Template Name: Wize - Creative Personal Portfolio
Version      : 1.0                                          
* ----------------------------------------------------------------------------------------
*/

/*
* ----------------------------------------------------------------------------------------
* 01.GLOBAL CSS STYLE
* 02.COMMON CSS STYLE
* 03.MENU CSS STYLE
* 04.HEADER CSS STYLE
* 05.ABOUT CSS STYLE
* 06.SERVICE CSS STYLE
* 07.SKILL CSS STYLE
* 08.RESUME CSS STYLE
* 09.PORTFOLIO CSS STYLE
* 10.TESTIMONIALS CSS STYLE
* 11.PRICING CSS STYLE
* 12.CLIENTS CSS STYLE
* 13.CONTACT CSS STYLE
* 14.FOOTER CSS STYLE
* 15.PRELOADER & BOUNCE CCS STYLE
* 16.BLOG CSS STYLE
* ----------------------------------------------------------------------------------------
*/


/*
* ----------------------------------------------------------------------------------------
* 01.GLOBAL STYLE
* ----------------------------------------------------------------------------------------
*/
:root {
    --main-color: rgb(180, 180, 180);
    --heading-color: rgb(29, 29, 38);
    --primary-color: rgb(255, 255, 255);
    --lighter-color: #1F1F1F;
    --subtitle-color: rgb(119, 119, 125);
    --black-color: #070707;
    --border-color: rgba(196, 240, 0, .6);
    --button-border: rgb(255, 255, 255);
    --body-background: #070707;
    --white-color: #fff;
}

* {
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    box-shadow: none;
}

main {
    position: relative;
    z-index: 5;
}

.o-hidden {
    overflow: hidden;
}

body {
    color: var(--main-color);
    background: var(--body-background);
    font-weight: 400;
    line-height: 30px;
    font-size: 16px;
    font-family: "DM Sans", sans-serif;
    overflow-x: hidden;

}



.heading,
input,
select,
textarea,
.nice-select,
.form-control,
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6,
.accordion-item .accordion-button {
    color: var(--main-color);
    font-family: "DM Sans", sans-serif;
}

h1,
.h1 {
    font-size: 58px;
}

h2,
.h2 {
    font-size: 22px;
}

h3,
.h3 {
    line-height: 1.3;
    font-size: 30px;
}

h4,
.h4 {
    line-height: 1.1;
    font-size: 24px;
}

h5,
.h5 {
    line-height: 1.4;
    font-size: 20px;
}

h6,
.h6 {
    font-size: 16px;
}


p {
    line-height: 28px;
}

ul,
li {
    margin: 0;
    padding: 0;
}

fieldset {
    border: 0 none;
    margin: 0 auto;
    padding: 0;
}

.no-padding {
    padding: 0
}

a {
    color: var(--main-color);
    cursor: pointer;
    outline: none;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    text-decoration: none;
    font-family: "DM Sans", sans-serif;
}

a:hover,
a:focus,
a:visited {
    text-decoration: none;
    outline: none;
}

a:hover {
    color: var(--primary-color);
}



ul,
li {
    list-style: none;
    padding: 0;
    margin: 0;
}

img {
    max-width: 100%;
    display: inline-block;
}

mark {
    color: var(--primary-color);
    background: transparent;
    text-decoration: underline;
}

header:after,
section:after,
footer:after {
    display: block;
    clear: both;
    content: "";
}

input,
select,
textarea,
.nice-select,
.form-control {
    width: 100%;
    height: auto;
    border: none;
    font-size: 20px;
    border-radius: 0;
    padding: 20px 0;
    background-color: #fff;
    border-bottom: 2px solid rgba(41, 41, 41, 0.1);
}

input::-webkit-input-placeholder,
select::-webkit-input-placeholder,
textarea::-webkit-input-placeholder,
.nice-select::-webkit-input-placeholder,
.form-control::-webkit-input-placeholder {
    color: var(--heading-color);
    opacity: .4;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
}

input:-ms-input-placeholder,
select:-ms-input-placeholder,
textarea:-ms-input-placeholder,
.nice-select:-ms-input-placeholder,
.form-control:-ms-input-placeholder {
    color: var(--heading-color);
    opacity: .4;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
}

input::-ms-input-placeholder,
select::-ms-input-placeholder,
textarea::-ms-input-placeholder,
.nice-select::-ms-input-placeholder,
.form-control::-ms-input-placeholder {
    color: var(--heading-color);
    opacity: .4;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
}

input::-webkit-input-placeholder,
select::-webkit-input-placeholder,
textarea::-webkit-input-placeholder,
.nice-select::-webkit-input-placeholder,
.form-control::-webkit-input-placeholder {
    color: var(--heading-color);
    opacity: .4;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
}

input::-moz-placeholder,
select::-moz-placeholder,
textarea::-moz-placeholder,
.nice-select::-moz-placeholder,
.form-control::-moz-placeholder {
    color: var(--heading-color);
    opacity: .4;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
}

input:-ms-input-placeholder,
select:-ms-input-placeholder,
textarea:-ms-input-placeholder,
.nice-select:-ms-input-placeholder,
.form-control:-ms-input-placeholder {
    color: var(--heading-color);
    opacity: .4;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
}

input::placeholder,
select::placeholder,
textarea::placeholder,
.nice-select::placeholder,
.form-control::placeholder {
    color: var(--heading-color);
    opacity: .4;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
}

input:focus,
select:focus,
textarea:focus,
.nice-select:focus,
.form-control:focus {
    border-bottom: 2px solid rgba(41, 41, 41, 0.1);
}

.nice-select {
    font-size: 20px;
    line-height: 1.3;
}

.nice-select:after {
    width: 8px;
    height: 8px;
    right: 8px;
    border-color: var(--heading-color);
}

.nice-select .current {
    font-weight: 500;
    color: var(--heading-color);
}

.nice-select .list {
    min-width: 100%;
    border-radius: 0;
}

textarea {
    display: inherit;
    padding-top: 20px;
}

label {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--main-color);
}

.form-group {
    position: relative;
    margin-bottom: 15px;
}

input:focus,
button:focus,
.form-control:focus {
    outline: none;
    box-shadow: none;
    border-color: #cfdbf1;
}

input[type=search]::-ms-clear {
    display: none;
    width: 0;
    height: 0;
}

input[type=search]::-ms-reveal {
    display: none;
    width: 0;
    height: 0;
}

input[type=search]::-webkit-search-decoration,
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-results-button,
input[type=search]::-webkit-search-results-decoration {
    display: none;
}

input[type=checkbox],
input[type=radio] {
    height: auto;
    width: auto;
}

html {
    scroll-behavior: unset !important;
}


/*
* ----------------------------------------------------------------------------------------
* 02.COMMON CSS STYLE
* ----------------------------------------------------------------------------------------
*/

.no-gap {
    margin-left: 0;
    margin-right: 0;
}

.no-gap>div {
    padding-left: 0;
    padding-right: 0;
}

.section-title {
    margin-top: -7px;
}

.section-title .sub-title {
    font-weight: 500;
    display: block;
    color: rgba(255, 255, 255, 0.65);
}


.section-title h2 {
    font-size: 80px;
    line-height: 90px;
    background: -webkit-linear-gradient(#fff, #696969);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 40px;
}
.section-title p {
    padding-left: 40px;
    padding-right: 400px;
    font-size: 20px;
    line-height: 28px;
}
.section-title h2 span {
    color: var(--primary-color);
}


.section-title h6 {
    font-size: 18px;
}

.section-title h6 span {
    color: var(--primary-color);
}

.theme-btn {
    background: var(--primary-color);
    color: #000000;
    cursor: pointer;
    display: inline-block;
    border: 1px solid var(--button-border);
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    margin: 0 5px 10px 0;
    overflow: visible;
    border-radius: 50px;
    padding: 17px 34px;
    text-align: center;
    text-transform: none;
    -webkit-transition: .3s;
    transition: .3s;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 4px 0px 0px;
}

.theme-btn:focus {
    text-decoration: none;
    -webkit-transition: .3s;
    transition: .3s
}

.theme-btn.call-to-action-button {
    background: var(--black-color);
    color: var(--main-color);
}

.theme-btn.call-to-action-button:hover {
    color: var(--white-color);
    font-size: 18px;
}

.theme-btn i {
    margin-left: 4px;
}

.theme-btn:hover {
    border: 1px solid var(--button-border);
    text-decoration: none;
    color: var(--white-color);
    -webkit-transition: .3s;
    transition: .3s;
    background: var(--black-color);
}

.theme-btn:active {
    box-shadow: rgba(0, 0, 0, .125) 0 3px 5px inset;
    outline: 0;
}

.details-btn {
    width: 50px;
    height: 50px;
    background: #000000;
    line-height: 50px;
    border-radius: 50%;
    text-align: center;
    display: inline-block;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    border: 1px solid var(--border-color);
}

.details-btn:hover {
    color: var(--black-color);
    background: var(--primary-color);
}

.read-more {
    color: white;
    font-size: 14px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    text-transform: capitalize;
}

.read-more i {
    float: right;
    margin-top: 2px;
    margin-left: 7px;
    -webkit-transition: 0.5s;
    transition: 0.5s;
}

.read-more:hover {
    color: var(--primary-color);
}

.read-more:hover i {
    margin-left: 10px;
}

.list-style-one li {
    color: white;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-weight: 500;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}


.list-style-one li:not(:last-child) {
    margin-bottom: 10px;
}

.list-style-one.two-column {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 30px;
}

.list-style-one.two-column li {
    color: var(--subtitle-color);
    font-size: 14px;
    border: 2px dashed var(--border-color);
    border-radius: 2em;
    margin: 0 15px 10px 0;
    padding: 0.5em 1em;
}


.list-style-one.two-column li i {
    padding-right: 10px;
}

.list-style-two li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}



.list-style-two li:before {
    content: "\f00c";
    line-height: 1;
    font-size: 16px;
    margin-right: 20px;
    color: var(--lighter-color);
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    font-family: "Font Awesome 5 Pro";
}

.list-style-two li:not(:last-child) {
    margin-bottom: 25px;
}

.social-style-one {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-left: -5px;
    margin-right: -5px;
}

.social-style-one a {
    color: #B0AFAF;
    font-size: 14px;
    margin-left: 5px;
    margin-right: 5px;
    width: 35px;
    height: 35px;
    background: #0C0B0B;
    line-height: 35px;
    border-radius: 50%;
    text-align: center;
}

.social-style-one a:hover {
    color: var(--black-color);
    background: var(--primary-color);
}

.social-style-two {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-left: -12px;
    margin-right: -12px;
}

.social-style-two a {
    color: rgba(255, 255, 255, 0.35);
    margin-left: 12px;
    margin-right: 12px;
}

.social-style-two a:hover {
    color: var(--primary-color);
}


.pagination {
    -webkit-box-align: center;
    -ms-flex-align: center;
    -ms-grid-row-align: center;
    align-items: center;
    margin-left: -15px;
    margin-right: -15px;
}

.pagination li {
    margin: 10px 15px 0;
}

.pagination li a,
.pagination li .page-link {
    padding: 0;
    border: none;
    font-size: 24px;
    box-shadow: none;
    font-weight: 600;
    color: var(--main-color);
    background: transparent;
}

.pagination li.active .page-link,
.pagination li:hover:not(.disabled) .page-link {
    background: transparent;
    color: var(--primary-color);
}

.ratting {
    line-height: 1;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-left: -5px;
    margin-right: -5px;
}

.ratting i {
    margin: 5px;
    color: #AB6034;
}

.slick-arrow {
    width: 40px;
    height: 40px;
    color: #fff;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    line-height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 1px solid var(--button-border);
}

.slick-arrow:focus,
.slick-arrow:hover {
    color: var(--black-color);
    background: transparent;
    border-color: var(--primary-color);
}

.slider-arrows button:first-child {
    margin-right: 5px;
}

.slider-arrows button:last-child {}

/*** Slick Dots ***/
.slick-dots {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.slick-dots li {
    position: relative;
    cursor: pointer;
    margin: 8px;
    width: 6px;
    height: 6px;
    opacity: 0.3;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
    -webkit-transition: 0.5s;
    transition: 0.5s;
    border-radius: 50%;
    background: #1D1B1A;
}

.slick-dots li button {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    height: 0;
}

.slick-dots li:before {
    content: '';
    width: 0;
    height: 0;
    left: 50%;
    top: 50%;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    border-radius: 50%;
    position: absolute;
    border: 1px solid var(--primary-color);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.slick-dots li.slick-active {
    background: var(--primary-color);
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.slick-dots li.slick-active:before {
    width: 21px;
    height: 21px;
}

.before-after-none:after,
.before-after-none:before {
    display: none;
}

.text-white *,
.text-white a,
.text-white .count-text,
.text-white .footer-newsletter-content .sub-title {
    color: white;
}

.text-white li li .dropdown-btn span {
    color: var(--heading-color);
}

.text-white .copyright-area p {
    opacity: 0.5;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
}

.text-white .copyright-area a {
    opacity: 0.5;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
}

.text-white .copyright-area a:hover {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.banner-inner h1 {
    font-size: 75px;
    font-weight: 500;
    margin-bottom: 16px;
    text-transform: capitalize;
}

.banner-inner h3 {
    margin-bottom: 20px;
}



.breadcrumb {
    padding: 0;
    margin: 0;
    font-size: 18px;
    background: transparent;
    text-transform: capitalize;
}



.breadcrumb .breadcrumb-item {
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.breadcrumb .breadcrumb-item a {
    color: white;
}

.breadcrumb .breadcrumb-item.active {
    color: var(--primary-color);
    text-decoration: underline;
}

.breadcrumb .breadcrumb-item+.breadcrumb-item:before {
    margin-left: 20px;
    margin-right: 20px;
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none;
    content: "";
    width: 5px;
    height: 5px;
    padding-right: 0;
    border-radius: 50%;
    background: white;
}






/*
* ----------------------------------------------------------------------------------------
* 03.MENU CSS STYLE
* ----------------------------------------------------------------------------------------
*/


.main-header {
    position: relative;
    left: 0px;
    top: 0px;
    z-index: 999;
    width: 100%;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}


.fixed-header .header-inner {
    padding: 10px 0px;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;

}

.header-inner {
    padding: 20px 0px;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.main-header .header-upper {
    z-index: 5;
    width: 100%;
    position: fixed;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}


.main-header .logo-outer {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none;
}


.main-header .logo {
    z-index: 9;
    padding: 2px 0;
    position: relative;
}

.main-header .logo img {
    width: 40px;
}

.main-header .nice-select .current {
    color: white;
}

.main-header.menu-absolute .header-upper {
    position: absolute;
}

.main-header.fixed-header .header-upper {
    top: 0;
    left: 0;
    position: fixed;
    -webkit-animation: easeIn 1s;
    animation: easeIn 1s;
    background: #000;
    border-bottom: 1px dashed rgba(119, 119, 125, .2);
}


.fixed-header .main-menu .navbar-collapse li a:hover {
    color: var(--primary-color);
}


.menu-social a {
    margin-left: 18px;
    margin-right: 18px;
}

.nav-outer {
    margin-left: auto;
}

.main-menu .mobile-logo {
    margin-right: auto;
}

.main-menu .mobile-logo img {
    width: 75px;
}



.main-menu .navbar-collapse {
    padding: 0px;
}

.main-menu .navbar-collapse>ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}



.main-menu .navbar-collapse li {
    padding: 12px 5px 12px 30px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}



.main-menu .navbar-collapse li.dropdown .dropdown-btn {
    cursor: pointer;
    font-size: 12px;
    margin-left: 5px;
    color: var(--heading-color);
}


.main-menu .navbar-collapse li a {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    display: block;
    font-size: 16px;
    font-weight: 500;
    text-transform: capitalize;
    position: relative;
    color: rgb(222, 222, 222);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.main-menu .navbar-collapse li a:after {
    content: '';
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    position: absolute;
    left: 0;
    top: 115%;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    width: 100%;
    height: 2px;
    background: var(--primary-color);
    -webkit-transform-origin: right;
            transform-origin: right;
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
}

.main-menu .navbar-collapse li a:hover {
    color: var(--primary-color);
    text-decoration: none;
}



.main-menu .navbar-collapse li.current>a,
.main-menu .navbar-collapse li.current-menu-item>a {
    font-weight: 500;
}

.main-menu .navbar-collapse li li {
    border-top: 1px solid var(--border-color);
}

.main-menu .navbar-collapse li li a {
    text-transform: capitalize;
}

.main-menu .navbar-collapse li li a:after {
    display: none;
}


.main-menu .navbar-collapse li ul li {
    width: 100%;
    padding: 7px 20px;
}



.main-menu .navbar-collapse li ul li ul {
    left: 100%;
    top: 0%;
}


.main-menu .navbar-header {
    display: none;
}



.main-menu .navbar-header .navbar-toggle {
    float: right;
    padding: 4px 0;
    cursor: pointer;
    background: transparent;
}

.main-menu .navbar-header .navbar-toggle .icon-bar {
    background: var(--main-color);
    height: 4px;
    width: 30px;
    display: block;
    margin: 5px 0;
}






/*
* ----------------------------------------------------------------------------------------
* 04.HEADER CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.single-page-hero-area {
    padding-top: 200px;
    padding-bottom: 100px;
    background-color: #130f40;
    background-image: -webkit-linear-gradient(135deg, #130f40 0%, #000000 74%);
    background-image: linear-gradient(315deg, #130f40 0%, #000000 74%);
}

.single-page-hero-area h2 {
    font-size: 80px;
    line-height: 90px;
    background: -webkit-linear-gradient(#fff, #696969);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 40px;
}

.single-page-hero-area p {
    padding-left: 40px;
    padding-right: 400px;
    font-size: 20px;
    line-height: 28px;
}

.main-hero-area {
    padding-top: 200px;
    padding-bottom: 100px;
    background-color: #130f40;
    background-image: -webkit-linear-gradient(135deg, #130f40 0%, #000000 74%);
    background-image: linear-gradient(315deg, #130f40 0%, #000000 74%);
}

.hero-content {
    margin-bottom: 60px;
    position: relative;
    padding-left: 80px;
}

.hero-content .dot-shape {
    position: absolute;
    left: 15%;
    top: 25%;
    -webkit-animation: rotated_circle linear 10s infinite;
    animation: rotated_circle linear 10s infinite;
}

.hero-content .dot-shape2 {
    position: absolute;
    right: 15%;
    top: 60%;
    -webkit-animation: rotated_circle linear 10s infinite;
    animation: rotated_circle linear 10s infinite;
}

.hero-content h2 {
    display: block;
    font-weight: 400;
    margin-bottom: 5px;
    color: var(--main-color);
    font-size: 20px;
    line-height: 28px;
}

.hero-content h1 {
    font-weight: 400;
    line-height: 80px;
    margin: 10px 0px;
    font-size: 64px;
    background: -webkit-linear-gradient(#fff, #696969);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero-btns {
    margin-top: 30px;
}

.hero-content .hero-btns .read-more {
    margin-top: 15px;
    text-decoration: underline;
}

.author-image-part {
    z-index: 1;
    overflow: hidden;
    max-width: 575px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    text-align: center;
    border-radius: 0 0 580px 565px;
}

.author-image-part .bg-circle {
    z-index: -2;
    width: 425px;
    height: 425px;
    position: absolute;
    left: 5%;
    top: 4%;
    border-radius: 50%;
    background: var(--black-color);
}


.author-image-part .progress-shape {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: -1;
    width: 100%;
    text-align: center;
}

.hero-counter-area {
    margin-top: 60px;
}


.counter-item {
    text-align: left;
}

.counter-title {
    color: var(--main-color);
}

.hero-counter-area .count-text {
    line-height: 1;
    font-weight: 700;
    color: var(--white-color);
    text-align: center;
    font-size: 50px;

}

.hero-counter-area .count-text.plus:after {
    content: '+';
}

.hero-counter-area .count-text.k-plus:after {
    content: 'k+';
}

.hero-counter-area .count-text.percent:after {
    content: '%+';
}

.hero-counter-area .counter-title {
    display: block;
}





/*
* ----------------------------------------------------------------------------------------
* 05.ABOUT CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.about-area {
    padding: 100px 0px;
    background-color: #130f40;
    background-image: -webkit-linear-gradient(220deg, #130f40 0%, #000000 74%);
    background-image: linear-gradient(230deg, #130f40 0%, #000000 74%);
}

.about-content-part {
    padding-left: 60px;
}

.about-content-part h2 {
    font-size: 40px;
    line-height: 48px;
    margin-bottom: 20px;
    text-transform: capitalize;
    color: var(--main-color);
}

.about-content-part h2 span {
    color: var(--primary-color);
}



.about-social ul {
    list-style: none;
}

.about-social ul li {
    display: inline-block;
    margin: 0px 15px;
}

.about-social ul li a {
    font-size: 20px;
}

.about-image-part .dot-shape {
    position: absolute;
    left: -10%;
    top: 25%;
    -webkit-animation: rotated_circle linear 10s infinite;
    animation: rotated_circle linear 10s infinite;
}



.about-btn {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    color: #fff;
    border: 1px solid rgb(237, 237, 238);
    padding: 15px 20px 10px 20px;
    border-radius: 30px;
}

.about-btn img {
    border-radius: 50%;
    margin-right: 15px;
}

.about-btn h6 {
    font-weight: 400;
    color: rgb(119, 119, 125);
    margin-bottom: 0;
    margin-right: 15px;
    margin-top: -5px;

}


.about-btn i {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}








.glitch {
    position: relative;
    overflow: hidden;
}

.glitch img {
    position: relative;
    z-index: 1;
    display: block;
    border-radius: 40px;
    border: 4px solid #130f40;
}

.glitch__layers {
    position: absolute;
    z-index: 2;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

.glitch__layer {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-image: url(../../assets/images/about/me.jpg);
    background-repeat: no-repeat;
    background-position: 0 0;
}

.glitch__layer:nth-child(1) {
    -webkit-transform: translateX(-5%);
            transform: translateX(-5%);
    -webkit-animation: glitch-anim-1 2s infinite linear alternate;
            animation: glitch-anim-1 2s infinite linear alternate;
}

.glitch__layer:nth-child(2) {
    -webkit-transform: translateX(3%) translateY(3%);
            transform: translateX(3%) translateY(3%);
    -webkit-animation: glitch-anim-2 2.3s -.8s infinite linear alternate;
            animation: glitch-anim-2 2.3s -.8s infinite linear alternate;
}

.glitch__layer:nth-child(3) {
    -webkit-transform: translateX(5%);
            transform: translateX(5%);
    -webkit-animation: glitch-anim-flash 1s infinite linear;
            animation: glitch-anim-flash 1s infinite linear;
}

@-webkit-keyframes glitch-anim-1 {
    0% {
        -webkit-clip-path: polygon(0 0%, 100% 0%, 100% 5%, 0 5%);
                clip-path: polygon(0 0%, 100% 0%, 100% 5%, 0 5%);
    }

    10% {
        -webkit-clip-path: polygon(0 15%, 100% 15%, 100% 15%, 0 15%);
                clip-path: polygon(0 15%, 100% 15%, 100% 15%, 0 15%);
    }

    20% {
        -webkit-clip-path: polygon(0 10%, 100% 10%, 100% 20%, 0 20%);
                clip-path: polygon(0 10%, 100% 10%, 100% 20%, 0 20%);
    }

    30% {
        -webkit-clip-path: polygon(0 1%, 100% 1%, 100% 2%, 0 2%);
                clip-path: polygon(0 1%, 100% 1%, 100% 2%, 0 2%);
    }

    40% {
        -webkit-clip-path: polygon(0 35%, 100% 35%, 100% 35%, 0 35%);
                clip-path: polygon(0 35%, 100% 35%, 100% 35%, 0 35%);
    }

    50% {
        -webkit-clip-path: polygon(0 45%, 100% 45%, 100% 46%, 0 46%);
                clip-path: polygon(0 45%, 100% 45%, 100% 46%, 0 46%);
    }

    60% {
        -webkit-clip-path: polygon(0 50%, 100% 50%, 100% 70%, 0 70%);
                clip-path: polygon(0 50%, 100% 50%, 100% 70%, 0 70%);
    }

    70% {
        -webkit-clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
                clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
    }

    80% {
        -webkit-clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
                clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
    }

    90% {
        -webkit-clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
                clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
    }

    100% {
        -webkit-clip-path: polygon(0 60%, 100% 60%, 100% 70%, 0 70%);
                clip-path: polygon(0 60%, 100% 60%, 100% 70%, 0 70%);
    }
}

@keyframes glitch-anim-1 {
    0% {
        -webkit-clip-path: polygon(0 0%, 100% 0%, 100% 5%, 0 5%);
                clip-path: polygon(0 0%, 100% 0%, 100% 5%, 0 5%);
    }

    10% {
        -webkit-clip-path: polygon(0 15%, 100% 15%, 100% 15%, 0 15%);
                clip-path: polygon(0 15%, 100% 15%, 100% 15%, 0 15%);
    }

    20% {
        -webkit-clip-path: polygon(0 10%, 100% 10%, 100% 20%, 0 20%);
                clip-path: polygon(0 10%, 100% 10%, 100% 20%, 0 20%);
    }

    30% {
        -webkit-clip-path: polygon(0 1%, 100% 1%, 100% 2%, 0 2%);
                clip-path: polygon(0 1%, 100% 1%, 100% 2%, 0 2%);
    }

    40% {
        -webkit-clip-path: polygon(0 35%, 100% 35%, 100% 35%, 0 35%);
                clip-path: polygon(0 35%, 100% 35%, 100% 35%, 0 35%);
    }

    50% {
        -webkit-clip-path: polygon(0 45%, 100% 45%, 100% 46%, 0 46%);
                clip-path: polygon(0 45%, 100% 45%, 100% 46%, 0 46%);
    }

    60% {
        -webkit-clip-path: polygon(0 50%, 100% 50%, 100% 70%, 0 70%);
                clip-path: polygon(0 50%, 100% 50%, 100% 70%, 0 70%);
    }

    70% {
        -webkit-clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
                clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
    }

    80% {
        -webkit-clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
                clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
    }

    90% {
        -webkit-clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
                clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
    }

    100% {
        -webkit-clip-path: polygon(0 60%, 100% 60%, 100% 70%, 0 70%);
                clip-path: polygon(0 60%, 100% 60%, 100% 70%, 0 70%);
    }
}

@-webkit-keyframes glitch-anim-2 {
    0% {
        -webkit-clip-path: polygon(0 15%, 100% 15%, 100% 30%, 0 30%);
                clip-path: polygon(0 15%, 100% 15%, 100% 30%, 0 30%);
    }

    15% {
        -webkit-clip-path: polygon(0 3%, 100% 3%, 100% 3%, 0 3%);
                clip-path: polygon(0 3%, 100% 3%, 100% 3%, 0 3%);
    }

    25% {
        -webkit-clip-path: polygon(0 8%, 100% 8%, 100% 20%, 0 20%);
                clip-path: polygon(0 8%, 100% 8%, 100% 20%, 0 20%);
    }

    30% {
        -webkit-clip-path: polygon(0 20%, 100% 20%, 100% 20%, 0 20%);
                clip-path: polygon(0 20%, 100% 20%, 100% 20%, 0 20%);
    }

    45% {
        -webkit-clip-path: polygon(0 45%, 100% 45%, 100% 45%, 0 45%);
                clip-path: polygon(0 45%, 100% 45%, 100% 45%, 0 45%);
    }

    50% {
        -webkit-clip-path: polygon(0 50%, 100% 50%, 100% 57%, 0 57%);
                clip-path: polygon(0 50%, 100% 50%, 100% 57%, 0 57%);
    }

    65% {
        -webkit-clip-path: polygon(0 60%, 100% 60%, 100% 60%, 0 60%);
                clip-path: polygon(0 60%, 100% 60%, 100% 60%, 0 60%);
    }

    75% {
        -webkit-clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
                clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
    }

    80% {
        -webkit-clip-path: polygon(0 40%, 100% 40%, 100% 60%, 0 60%);
                clip-path: polygon(0 40%, 100% 40%, 100% 60%, 0 60%);
    }

    95% {
        -webkit-clip-path: polygon(0 45%, 100% 45%, 100% 60%, 0 60%);
                clip-path: polygon(0 45%, 100% 45%, 100% 60%, 0 60%);
    }

    100% {
        -webkit-clip-path: polygon(0 11%, 100% 11%, 100% 15%, 0 15%);
                clip-path: polygon(0 11%, 100% 11%, 100% 15%, 0 15%);
    }
}

@keyframes glitch-anim-2 {
    0% {
        -webkit-clip-path: polygon(0 15%, 100% 15%, 100% 30%, 0 30%);
                clip-path: polygon(0 15%, 100% 15%, 100% 30%, 0 30%);
    }

    15% {
        -webkit-clip-path: polygon(0 3%, 100% 3%, 100% 3%, 0 3%);
                clip-path: polygon(0 3%, 100% 3%, 100% 3%, 0 3%);
    }

    25% {
        -webkit-clip-path: polygon(0 8%, 100% 8%, 100% 20%, 0 20%);
                clip-path: polygon(0 8%, 100% 8%, 100% 20%, 0 20%);
    }

    30% {
        -webkit-clip-path: polygon(0 20%, 100% 20%, 100% 20%, 0 20%);
                clip-path: polygon(0 20%, 100% 20%, 100% 20%, 0 20%);
    }

    45% {
        -webkit-clip-path: polygon(0 45%, 100% 45%, 100% 45%, 0 45%);
                clip-path: polygon(0 45%, 100% 45%, 100% 45%, 0 45%);
    }

    50% {
        -webkit-clip-path: polygon(0 50%, 100% 50%, 100% 57%, 0 57%);
                clip-path: polygon(0 50%, 100% 50%, 100% 57%, 0 57%);
    }

    65% {
        -webkit-clip-path: polygon(0 60%, 100% 60%, 100% 60%, 0 60%);
                clip-path: polygon(0 60%, 100% 60%, 100% 60%, 0 60%);
    }

    75% {
        -webkit-clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
                clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
    }

    80% {
        -webkit-clip-path: polygon(0 40%, 100% 40%, 100% 60%, 0 60%);
                clip-path: polygon(0 40%, 100% 40%, 100% 60%, 0 60%);
    }

    95% {
        -webkit-clip-path: polygon(0 45%, 100% 45%, 100% 60%, 0 60%);
                clip-path: polygon(0 45%, 100% 45%, 100% 60%, 0 60%);
    }

    100% {
        -webkit-clip-path: polygon(0 11%, 100% 11%, 100% 15%, 0 15%);
                clip-path: polygon(0 11%, 100% 11%, 100% 15%, 0 15%);
    }
}

@-webkit-keyframes glitch-anim-flash {
    0% {
        opacity: .2;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
    }

    30%,
    100% {
        opacity: 0;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    }
}

@keyframes glitch-anim-flash {
    0% {
        opacity: .2;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
    }

    30%,
    100% {
        opacity: 0;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    }
}

/*
* ----------------------------------------------------------------------------------------
* 06.SERVICE CSS STYLE
* ----------------------------------------------------------------------------------------
*/

.services-area {
    padding: 100px 0px;
    background-color: #130f40;
    background-image: -webkit-linear-gradient(220deg, #130f40 0%, #000000 74%);
    background-image: linear-gradient(230deg, #130f40 0%, #000000 74%);

}

.service-item {
    -webkit-transition: 0.5s;
    transition: 0.5s;
    padding: 40px 20px;
    border-radius: 12px;
    border: 1px solid var(--black-color);
    background: var(--black-color);
}


.service-item .content p {
    margin-bottom: 0;
    color: rgb(119, 119, 125);
    font-size: 16px;
    line-height: 22px;
}

.service-item .content h4 {
    margin: 12px 0px;
    color: var(--main-color);
    font-size: 22px;
    line-height: 29px;
}

.service-item .content i {
    font-size: 50px;
    color: var(--primary-color);
    margin-bottom: 25px;
}


.service-item .details-btn {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none;
    margin-left: auto;
    -ms-flex-item-align: center;
    -ms-grid-row-align: center;
    align-self: center;
}

.service-item:hover {
    border-color: var(--primary-color);
}

.service-item:hover .details-btn {
    color: var(--black-color);
    background: var(--primary-color);
}

.t9ad {
    display: flex;               /* Use flexbox for centering */
    flex-direction: column;      /* Align items in a column */
    align-items: center;         /* Center horizontally */
    margin-top: 40px;           /* Add top margin */
    text-align: center;   
    align-self: center;
}

/*
* ----------------------------------------------------------------------------------------
* 07.SKILL CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.skill-area {
    padding-top: 20px;
}

.skill-item {
    -webkit-transition: 0.5s;
    transition: 0.5s;
    text-align: center;
    margin-bottom: 30px;
    border-radius: 14px;
    padding: 25px;
    border: 1px dashed var(--border-color);
}

.skill-item h5 {
    margin-top: 20px;
    color: var(--subtitle-color);
    font-size: 16px;
    line-height: 19px;
}

.skill-item .percent {
    padding: 10px;
    display: block;
    font-size: 20px;
    font-weight: 600;
    color: var(--black-color);
}

.skill-item:hover {
    border-color: var(--primary-color);
}

.skill-item:hover .percent {
    color: var(--heading-color);
    background: var(--black-color);
}

.extra-skills {}

.extra-skills ul {
    margin-top: 30px;
}

ul.extra-skills li {
    border: 1px dashed var(--border-color);
    display: inline-block;
    border-radius: 12px;
    padding: 3px 10px;
    margin-bottom: 10px;
    margin-right: 20px;
}

ul.extra-skills li i {
    padding-right: 10px;
}

/*
* ----------------------------------------------------------------------------------------
* 08.RESUME CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.resume-area {
    padding: 100px 0px;

}

.resume-wrapper {
    position: relative;
    padding-left: 20px;
    margin-top: 50px;
}

.resume-wrapper:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    border-left: 1px solid var(--main-color);
}

.resume-wrapper i {
    color: var(--main-color);
    font-size: 40px;
    margin-bottom: 30px;
    display: block;
}

.resume-wrapper .resume-box {
    margin-bottom: 40px
}
.resume-box h5{
    font-size: 30px;
    color: var(--main-color);
}
.resume-wrapper .resume-box:last-child {
    margin-bottom: 0
}

.resume-wrapper .resume-box .resume-date {
    position: relative;
    display: inline-block;
    border: 1px solid var(--main-color);
    border-radius: 2em;
    margin-bottom: 1em;
    color: var(--primary-color);
    padding: 0.5em 1em;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
    -webkit-transition: linear 0.1s;
    transition: linear 0.1s
}

.resume-wrapper .resume-box .resume-date:before {
    content: '';
    position: absolute;
    top: 50%;
    left: -20px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 20px;
    height: 1px;
    border-top: 1px solid var(--main-color);
}

.resume-wrapper .resume-box .resume-date:after {
    content: '';
    position: absolute;
    top: 50%;
    left: -23px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    background: var(--main-color);
    width: 5px;
    height: 5px;
    border-radius: 50%
}

.resume-wrapper .resume-box:hover .resume-date {
    color: var(--primary-color)
}




/*
* ----------------------------------------------------------------------------------------
* 09.PORTFOLIO CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.projects-area {
    padding-top: 50px;
    padding-bottom: 100px;
    background-color: #000000;
    background-image: -webkit-linear-gradient(220deg, #000000 0%, #000000 100%);
    background-image: linear-gradient(230deg, #130f40 0%, #000000 100%);
}

.projects-areea {
    padding-top: 50px;
    padding-bottom: 100px;
    background-color: #130f40;
    background-image: -webkit-linear-gradient(220deg, #130f40 0%, #000000 74%);
    background-image: linear-gradient(230deg, #000000 0%, #000000 74%);
}


.filter ul {
    margin: 0;
    padding: 0
}

.filter ul li {
    position: relative;
    display: inline-block;
    border: 1px dashed black;
    border-radius: 2em;
    margin: 0 7px 10px 0;
    padding: 0.5em 1em;
    font-size: 0.9em;
    font-family: "Roboto Mono", monospace;
    color: black;
    cursor: pointer;
    -webkit-transition: linear 0.1s;
    transition: linear 0.1s
}

.filter ul li:hover,
.filter ul li.mixitup-control-active {
    background: black;
    color: white
}

.theme-dark .filter ul li {
    border-color: white;
    color: white
}

.theme-dark .filter ul li:hover,
.theme-dark .filter ul li.mixitup-control-active {
    background: white;
    color: black
}

.portfolio-box {
    position: relative;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    overflow: hidden;
    border-radius: 0.5em
}

.portfolio-box:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    background-image: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.3), transparent);
    background-image: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
    width: 100%;
    height: 100%;
    -webkit-transition: ease-out 0.16s;
    transition: ease-out 0.16s
}

.portfolio-box img {
    -webkit-transform: scale(1);
    transform: scale(1);
    width: 100%;
    -webkit-transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    -webkit-transition: -webkit-transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: -webkit-transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), -webkit-transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1)
}

.portfolio-box .portfolio-category {
    position: absolute;
    top: 16px;
    right: 16px;
    display: inline-block;
    background: rgba(0, 0, 0, 0.2);
    -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
    border-radius: 2em;
    padding: 0.5em 1em;
    color: white;
    font-weight: 400;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px
}

.portfolio-box .portfolio-caption {
    z-index: 1;
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    visibility: hidden;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    margin-bottom: -6px;
    padding: 0 30px 24px;
    -webkit-transition: ease-out 0.5s;
    transition: ease-out 0.5s
}

.portfolio-box .portfolio-caption * {
    margin: 0;
    color: white;
    font-size: 30px;
    font-weight: 600;
    letter-spacing: 0.5px;
    -webkit-transition: linear 0.06s;
    transition: linear 0.06s
}

.portfolio-box .portfolio-caption *:hover {
    text-shadow: 0 0 white
}

.portfolio-box:hover:after {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)"
}

.portfolio-box:hover img {
    -webkit-transform: scale(1.04);
    transform: scale(1.04);
    -webkit-filter: blur(1.5px);
    filter: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"><filter id="filter"><feGaussianBlur stdDeviation="1.5" /></filter></svg>#filter');
    filter: blur(1.5px)
}

.portfolio-box:hover .portfolio-caption {
    visibility: visible;
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    margin-bottom: 0
}




.single-project-page-right,
.single-project-page-left {
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.single-project-page-design {
    padding-top: 30px;
}

.single-project-image img {
    width: 100%;
}

.single-info h3 {
    font-size: 20px;
}
.single-image{
    margin-bottom: 30px;
}
.single-info {
    padding-bottom: 30px;
}
.single-info:last-child{
    padding-bottom: 0px;
}

.single-info p {
    margin-bottom: 0;
}

.project-image {
    margin-bottom: 45px;
}

.single-project-item {
    padding: 30px;
    border: 1px solid var(--border-color);
}

/*
* ----------------------------------------------------------------------------------------
* 10.TESTIMONIALS CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.testimonials-area {
    padding-top: 20px;
}

.testimonial-item {
    -webkit-transition: 0.5s;
    transition: 0.5s;
    padding: 20px 20px;
    border-radius: 16px;
    border: 1px dashed var(--border-color);
}


.testimonial-item .author {
    margin-bottom: 25px;
    display: inline-block;
}

.testimonial-item .author img {
    display: inline-block;
    border-radius: 50%;
    height: 60px;
    width: 60px;
}


.testimonial-item .text {
    margin-bottom: 30px;
}

.testimonial-item .testi-des h5 {
    margin-bottom: 0;
    color: var(--subtitle-color);
    font-size: 16px;
    line-height: 19px;
}


.testimonial-item .testi-des span {
    margin-bottom: 0;
    color: rgb(119, 119, 125);
    font-size: 14px;
    line-height: 22px;
}

.testimonial-item:hover {
    border: 1px solid var(--primary-color);
}

.testimonial-item:hover .author:before {
    color: var(--black-color);
    background: var(--primary-color);
}

.testimonials-wrap {
    margin-left: -15px;
    margin-right: -15px;
}

.testimonials-wrap .testimonial-item {
    margin-left: 15px;
    margin-right: 15px;
}






/*
* ----------------------------------------------------------------------------------------
* 11.PRICING CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.pricing-area {
    padding-top: 20px;
}

.pricing-item {
    padding: 10px;
    border-radius: 16px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.pricing-item .pricing-header {
    padding: 20px 20px;
    border-radius: 16px;
    border: 1px solid var(--border-color);
}

.pricing-header h4.title {
    margin: 12px 0px;
    color: var(--subtitle-color);
    font-size: 16px;
    line-height: 19px;
    padding-bottom: 20px;
}

.pricing-item .pricing-header .save-percent {
    margin-bottom: 20px;
    color: rgb(119, 119, 125);
    font-size: 14px;
    line-height: 17px;
}

.pricing-item .pricing-header .save-percent span {
    color: var(--primary-color);
}

.pricing-item .pricing-header .price {
    font-size: 38px;
    font-weight: 500;
    color: var(--primary-color);
}



.pricing-item .pricing-header .price:before {
    content: '$';
}

.pricing-item .pricing-header .price:after {
    font-size: 16px;
    font-weight: 400;
    color: var(--main-color);
    content: '/Hour';
}

.pricing-item .pricing-details {
    padding: 20px 20px;
}



.pricing-item .pricing-details p {
    margin-bottom: 35px;
}

.pricing-item .pricing-details ul {
    padding-bottom: 12px;
}

.pricing-item .pricing-details ul li {
    color: rgb(119, 119, 125);
    font-size: 14px;
    margin-bottom: 10px;
}

.pricing-item .pricing-details ul li i {
    padding-right: 10px;
}


.pricing-item .pricing-details ul li.unable {
    color: var(--main-color);
    opacity: 0.35;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=35)";
}

.pricing-item .pricing-details ul li.unable:before {
    color: white;
}






/*
* ----------------------------------------------------------------------------------------
* 12.CLIENTS CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.client-logo-area {
    padding-top: 20px;
}

.client-logo-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-left: -30px;
    margin-right: -30px;
}



.client-logo-wrap .client-logo-item {
    width: calc(20% - 60px);
    margin: 0 30px 10px;
}


/*
* ----------------------------------------------------------------------------------------
* 13.CONTACT CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.contact-area {
    padding: 100px 0px;
}

.contact-form-area .form-group {
    margin-bottom: 32px;
}

.contact-form-area .form-group .for-icon {
    right: 26px;
    font-size: 16px;
    margin-bottom: 0;
    position: absolute;
    margin-top: -45px;
    color: #000;
}

.contact-form-area .form-group .form-control {
    font-size: 16px;
    color: #000;
    font-weight: 400;
    line-height: 1.3;
    padding: 19px 20px 19px 20px;
    background: #F9F9F9;
    font-family: "DM Sans", sans-serif;
    border: 1px dashed var(--border-color);
    border-radius: 10px;
}

.contact-form-area .form-group .form-control:focus {
    box-shadow: none;
    background: #F9F9F9;
    border-color: var(--primary-color);
}


.contact-form-area .form-group label {
    font-size: 16px;
    color: var(--main-color);
    font-weight: 500;
    margin-bottom: 18px;
    font-family: "DM Sans", sans-serif;
}

.contact-form-area .form-group .nice-select .current {
    font-weight: 400;
    color: rgba(255, 255, 255, 0.2);
}

.contact-form-area .form-group .nice-select:focus .current {
    color: white;
}

.contact-form-area .form-group .nice-select:focus:after {
    border-color: white;
}

.contact-form-area .form-group .nice-select .list {
    background: var(--black-color);
}

.contact-form-area .form-group .nice-select .option.focus,
.contact-form-area .form-group .nice-select .option.selected.focus,
.contact-form-area .form-group .nice-select .option:hover {
    background-color: var(--heading-color);
}

.contact-form-area .form-group .nice-select:after {
    right: 26px;
    margin-top: -6px;
    border-color: rgba(255, 255, 255, 0.2);
}

.contact-icon {
    padding-bottom: 1px;
}

.contact-icon i {
    font-size: 20px;
    color: var(--primary-color);
}

.single-contact {
    padding-bottom: 20px;
}

.single-contact h2 {
    font-size: 16px;
    margin-top: 5px;
    margin-bottom: 5px;
    text-transform: capitalize;
    font-weight: 500;
    -webkit-transition: .3s;
    transition: .3s;
    color: var(--subtitle-color);
}


.contact-page-content h6 {
    font-size: 18px;
    margin-bottom: 22px;
}

.contact-page-content .social-style-one a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    color: var(--lighter-color);
}

.contact-page-content .social-style-one a:not(:hover) {
    background: white;
}

.contact-page-form {
    padding: 45px 50px 50px;
    border-radius: 14px;
    background: var(--lighter-color);
    border: 1px solid var(--border-color);
}



.contact-page-form .form-group .nice-select,
.contact-page-form .form-group .form-control {
    background: rgba(255, 255, 255, 0.07);
}

.our-location iframe {
    height: 600px;
}



.has-error .help-block.with-errors {
    margin-top: 5px;
    color: red;
}

#msgSubmit.h4 {
    font-size: 20px;
    margin-bottom: 0;
    margin-top: 10px;
}




/*
* ----------------------------------------------------------------------------------------
* 14.FOOTER CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.company-design-area {
    padding: 100px 100px;
}

.company-design-area h2 {
    text-transform: uppercase;
    font-size: 16px;
    text-align: center;
    color: #fff;
    margin-bottom: 30px;
    letter-spacing: 5px;
    font-weight: 500;
}

.footer-bottom {}

.call-to-action-part {
    padding: 50px 100px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 20px;
    box-shadow: rgba(255, 255, 255, 0.025) 0px 0.362176px 0.651917px -1px inset, rgba(255, 255, 255, 0.036) 0px 3px 5.4px -2px inset;
    background-color: var(--primary-color);
}

.call-to-action-area {
    padding-top: 15px;

}

.call-to-action-part h2 {
    font-size: 60px;
    line-height: 70px;
    margin-bottom: 20px;
    font-weight: 400;
    color: var(--black-color);
}

.call-to-action-part p {
    font-size: 20px;
    color: var(--lighter-color);
}

.footer-bottom .copyright-text {
    color: var(--main-color);
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
}

.footer-bottom .copyright-text a {
    color: var(--primary-color);
}


/*
* ----------------------------------------------------------------------------------------
* 15.PRELOADER & BOUNCE CCS STYLE
* ----------------------------------------------------------------------------------------
*/

body.loaded {
    overflow: hidden !important;
    height: 100% !important;
}

.preloader {
    position: fixed;
    z-index: 10;
    height: 100vh;
    width: 100%;
    left: 0;
    top: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: transparent;
    z-index: 99999999999999;
}

.preloader svg {
    position: absolute;
    top: 0;
    width: 100vw;
    height: 110vh;
    fill: var(--tj-black-2);
}

.preloader .preloader-heading .load-text {
    font-size: 20px;
    font-weight: 200;
    letter-spacing: 15px;
    text-transform: uppercase;
    z-index: 20;
}

.load-text span {
    -webkit-animation: loading 1s infinite alternate;
    animation: loading 1s infinite alternate;
}

.load-text span:nth-child(1) {
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
}

.load-text span:nth-child(2) {
    -webkit-animation-delay: 0.1s;
    animation-delay: 0.1s;
}

.load-text span:nth-child(3) {
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s;
}

.load-text span:nth-child(4) {
    -webkit-animation-delay: 0.3s;
    animation-delay: 0.3s;
}

.load-text span:nth-child(5) {
    -webkit-animation-delay: 0.4s;
    animation-delay: 0.4s;
}

.load-text span:nth-child(6) {
    -webkit-animation-delay: 0.5s;
    animation-delay: 0.5s;
}

.load-text span:nth-child(7) {
    -webkit-animation-delay: 0.6s;
    animation-delay: 0.6s;
}

@-webkit-keyframes loading {
    0% {
        opacity: 1;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    }

    100% {
        opacity: 0;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    }
}

@keyframes loading {
    0% {
        opacity: 1;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    }

    100% {
        opacity: 0;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    }
}


.color-pulse {
    background: #00FF00;
}

.circle {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    box-shadow: 0px 0px 1px 1px #0000001a;
}

.pulse {
    -webkit-animation: pulse-animation 2s infinite;
    animation: pulse-animation 2s infinite;
}

@-webkit-keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0px rgba(0, 255, 0, 0.2);
    }

    100% {
        box-shadow: 0 0 0 20px rgba(0, 255, 0, 0);
    }
}

@keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0px rgba(0, 255, 0, 0.2);
    }

    100% {
        box-shadow: 0 0 0 20px rgba(0, 255, 0, 0);
    }
}

/* ===== # Magic Cursor ===== */
#magic-cursor {
    position: absolute;
    left: 0;
    top: 0;
    width: 30px;
    height: 30px;
    pointer-events: none;
    z-index: 10000;
    -webkit-transition: opacity 0.2s ease-in-out 0.5s;
    transition: opacity 0.2s ease-in-out 0.5s;
}

#ball {
    position: fixed;
    width: 40px;
    height: 40px;
    border: 2px solid #999999;
    border-radius: 50%;
    pointer-events: none;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    box-sizing: border-box;
    /* -webkit-transform: scale(0.5);
    transform: scale(0.5); */
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    /* -webkit-transition: 0.2s;
    transition: 0.2s; */
    margin-left: -20px;
    margin-top: -20px;
}

.magic-cursor.cursor-hover {
    margin-left: -40px;
    margin-top: -40px;
    width: 80px;
    height: 80px;
    background-color: #cc0000;
    opacity: .3;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
}

#ball.hovered {
    -webkit-transition: opacity .3s;
    transition: opacity .3s;
    opacity: 0 !important;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}

.progress-wrap {
    position: fixed;
    bottom: 30px;
    right: 30px;
    height: 44px;
    width: 44px;
    cursor: pointer;
    display: block;
    border-radius: 50px;
    z-index: 100;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    visibility: hidden;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
    -webkit-transition: all 400ms linear;
    transition: all 400ms linear;
    mix-blend-mode: difference;
}

.progress-wrap.active-progress {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    visibility: visible;
    -webkit-transform: translateY(0);
    transform: translateY(0);
}

.progress-wrap i {
    position: absolute;
    left: 10px;
    top: 8px;
    font-size: 25px;
    text-align: center;
}

.progress-wrap svg path {
    fill: none;
}

.progress-wrap svg.progress-circle path {
    stroke: var(--main-color);
    stroke-width: 4;
    box-sizing: border-box;
    -webkit-transition: all 400ms linear;
    transition: all 400ms linear;
}

.progress-wrap {
    right: 10px;
    bottom: 20px;
}

.scroller__inner {
    margin-left: 100px;
    margin-top: 50px;
    padding-block: 16px;
    padding-block: 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    gap: 80px;
    gap: 5rem;
}

.scroller__inner img {
    margin-right: 60px;
    margin-left: 60px;
    margin-top: 10px;
    cursor: pointer;
    filter: grayscale(100%);
}

.scroller__inner img:hover{
    filter: grayscale(0);
}
.scroller[data-animated="true"] {
    overflow: hidden;
    -webkit-mask: -webkit-linear-gradient(left,
        transparent,
        white 20%,
        white 80%,
        transparent);
    mask: linear-gradient(90deg, transparent, white 20%, white 80%, transparent);
}

.scroller[data-animated="true"] .scroller__inner {
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    -webkit-animation: scroll var(--_animation-duration, 40s) var(--_animation-direction, forwards) linear infinite;
            animation: scroll var(--_animation-duration, 40s) var(--_animation-direction, forwards) linear infinite;
}

.scroller[data-direction="right"] {
    --_animation-direction: reverse;
}

.scroller[data-direction="left"] {
    --_animation-direction: forwards;
}

.scroller[data-speed="fast"] {
    --_animation-duration: 20s;
}

.scroller[data-speed="slow"] {
    --_animation-duration: 60s;
}

@-webkit-keyframes scroll {
    to {
        -webkit-transform: translate(calc(-50% - 0.5rem));
                transform: translate(calc(-50% - 0.5rem));
    }
}

@keyframes scroll {
    to {
        -webkit-transform: translate(calc(-50% - 0.5rem));
                transform: translate(calc(-50% - 0.5rem));
    }
}



.tag-list {
    margin: 0;
    padding-inline: 0;
    list-style: none;
}

.tag-list li {
    padding: 16px;
    padding: 1rem;
    background: var(--clr-primary-400);
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem -0.25rem var(--clr-primary-900);
}

/* for testing purposed to ensure the animation lined up correctly */
.test {
    background: red !important;
}

/*
* ----------------------------------------------------------------------------------------
* 16.BLOG CSS STYLE
* ----------------------------------------------------------------------------------------
*/
.blog-area {
    padding: 100px 0px;
}
.blog-post-box{
    margin-bottom: 60px;
}
.blog-post-box .blog-post-img {
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    border-radius: 0.5em
}

.blog-post-box .blog-post-img img {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    -webkit-transition: -webkit-transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: -webkit-transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), -webkit-transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1)
}

.blog-post-box .blog-post-img .blog-post-category {
    position: absolute;
    top: 16px;
    left: 16px;
    background: rgba(0, 0, 0, 0.2);
    -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
    border-radius: 2em;
    padding: 0.5em 1em;
    color: white;
    font-weight: 400;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px
}

.blog-post-box .blog-post-img .blog-post-category a {
    color: white
}

.blog-post-box .blog-post-img .blog-post-category a:hover {
    color: white
}

.blog-post-box .blog-post-img:hover img {
    -webkit-transform: scale(1.04);
    transform: scale(1.04);
    -webkit-filter: blur(1.5px);
    filter: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"><filter id="filter"><feGaussianBlur stdDeviation="1.5" /></filter></svg>#filter');
    filter: blur(1.5px)
}

.blog-post-caption{
    padding-left: 60px;
}

.blog-post-caption h2 a{
    display: block;
    font-size: 40px;
    padding: 10px 0px;
    color: var(--white-color);
}

.blog-post-caption h3{
    font-size: 16px;
    text-transform: uppercase;
    color: var(--main-color);
}


.blog-category{
    padding: 80px 0px;
}
.single-blog-post {
    margin-bottom: 50px;
}

.single-blog-post h2 {
    font-size: 22px;
}

.single-blog-post a.read-more {
    background: #000;
    color: #fff;
    padding: 6px 20px;
    border: 1px solid #000;
}

.single-blog-post a.read-more:hover {
    background: transparent;
    color: #000;
    border: 1px solid #000;
}

.post-date {
    margin-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 30px;
}

.post-date span {
    margin-right: 20px;
    color: var(--heading-color);
    background: var(--primary-color);
    padding: 10px 20px;
    border-radius: 10px;
    text-transform: uppercase;
}

.post-date span i {
    margin-right: 5px;
    color: var(--black-color);
}

.single-blog-post p {
    font-size: 16px;
    margin-bottom: 20px;
}

.blog-carosel-control {
    position: absolute;
    top: 50%;
    color: #000;
    font-size: 30px;
    margin-top: -50px;
    background: #333;
    height: 100px;
    width: 25px;
    text-align: center;
    line-height: 99px;
}

.blog-carosel-control.right {
    right: 0px;
    left: auto;
}

.pagination {
    margin: 0;
}

ul.blog_pagination li a {
    border: 1px solid #e8e8e9;
    border-radius: 0 !important;
    color: #353535;
    font-size: 16px;
    height: 35px;
    line-height: 20px;
    margin: 5px;
    width: 35px;
}

.black-icon {
    background: #333 none repeat scroll 0 0 !important;
    color: #fff !important;
}

.black-icon:hover {
    background: #333 !important;
}

.black-icon i {
    font-size: 14px;
}

ul.blog_pagination li a:hover {
    color: #fff;
    background: #333;
    border: 1px solid #333;
}

.single-side-bar {
    margin-bottom: 40px;
    overflow: hidden;
}

.single-side-bar h2 {
    margin-bottom: 15px;
    font-size: 22px;
    margin-top: 0;
    text-transform: capitalize;
}

.single-side-bar input {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    border: 1px solid #333;
    border-radius: 0;
    box-shadow: none;
    color: #e3e8f0;
    height: 50px;
    float: left;
    padding-left: 10px;
}

.single-side-bar input:hover,
.single-side-bar input:focus {
    border: 1px solid #333;
    box-shadow: none;
    outline: 0 none;
}

.single-side-bar button {
    background: #333 none repeat scroll 0 0;
    border: 1px solid #333;
    color: #fff;
    height: 42px;
    padding: 0 32px;
    -webkit-transition: .3s;
    transition: .3s;
}

.single-side-bar button:hover {
    background: transparent;
    color: #fff;
    border: 1px solid #333;
    -webkit-transition: .3s;
    transition: .3s;
}

.single-side-bar ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.single-side-bar ul li a {
    color: #fff;
    display: block;
    font-size: 16px;
    padding: 5px 0;
    opacity: .7;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=70)";
}

.single-side-bar ul li a span {
    float: right;
}

.single-side-bar ul li a:hover {
    color: var(--primary-color);
}

.single-blog-social-icon {
    overflow: hidden;
    float: left;
    width: 177px;
    margin-bottom: 10px;
}

.single-blog-social-icon i {
    border: 1px solid #333;
    border-radius: 50px;
    float: left;
    font-size: 22px;
    height: 50px;
    line-height: 50px;
    margin-right: 15px;
    text-align: center;
    width: 50px;
}

.single-blog-social-icon .facebook {
    border: 1px solid #5d82d1;
    color: #5d82d1;
}

.single-blog-social-icon .twitter {
    border: 1px solid #00BFF3;
    color: #00BFF3;
}

.single-blog-social-icon .youtube {
    border: 1px solid #CB1D1F;
    color: #CB1D1F;
}

.single-blog-social-icon .youtube-play {
    border: 1px solid #CB1D1F;
    color: #CB1D1F;
}

.single-blog-social-icon .behance {
    border: 1px solid #0068FF;
    color: #0068FF;
}

.single-blog-social-icon .dribbble {
    border: 1px solid #EA4C8A;
    color: #EA4C8A;
}

.tag {
    overflow: hidden;
}

.tag a {
    color: #fff;
    float: left;
    font-size: 14px;
    font-weight: 300;
    border-radius: 12px;
    margin: 5px 7px;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
    padding: 8px 20px;
    border: 1px solid var(--border-color);
}

.tag a:hover {
    color: #ccc;
    border: 1px solid #333;
    background: transparent;
}

.single-blog-post-details blockquote {
    font-size: 16px;
    line-height: 28px;
    padding-left: 40px;
    border-left: 1px solid #fff;
}

.single-blog-post-details img {
    width: 100%;
}

.single-blog-post-details h2 {
    padding: 20px 0px;
    font-size: 40px;
    color: var(--white-color);
}

.next-previews-button-design {
    margin-top: 40px;
    overflow: hidden;
}

.next-previews-button-design a {
    color: #000;
}

.post-comments-area {
    overflow: hidden;
    margin-top: 50px;
}

.post-comments-area h2 {
    margin-bottom: 20px;
}

.single-comment {
    overflow: hidden;
    margin-bottom: 60px;
}

.single-comment img {
    float: left;
    margin-right: 50px;
    margin-bottom: 80px;
    width: 100px;
    border-radius: 50%;
}

.single-comment h5 {
    font-weight: 700;
}

.single-comment p {}

.single-comment a {
    color: #fff;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
}

.single-comment a:hover {
    color: #333;
}

.single-comment a i {
    font-size: 10px;
    background: #767676 none repeat scroll 0 0;
    color: #fff;
    padding: 5px;
    margin-right: 5px;
    border-radius: 30px;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
}

.single-comment a:hover i {
    background: #333 none repeat scroll 0 0;
    border-radius: 30px;
    color: #fff;
    font-size: 10px;
    padding: 5px;
}

.comment-form-area {
    margin-left: 150px;
    background: #191919;
    padding: 50px 30px;
    border-radius: 12px;
}

.comment-form-area input {
    background: var(--white-color);
    border: 1px solid #e3e8f0;
    box-shadow: none;
    color: var(--black-color);
    height: 50px;
    border-radius: 0;
    padding-left: 10px;
}


.comment-form-area input:hover,
.comment-form-area input:focus {
    border: 1px solid var(--border-color);
    box-shadow: none;
    outline: 0 none;
}

.comment-form-area textarea {
    background: var(--white-color);
    border: 1px solid #e3e8f0;
    box-shadow: none;
    color: var(--black-color);
    height: 150px;
    border-radius: 0;
    padding-left: 10px;
}

.comment-form-area textarea:hover,
.comment-form-area textarea:focus {
    border: 1px solid var(--border-color);
    box-shadow: none;
    outline: 0 none;
}

.comment-form-area button {
    background: var(--primary-color) none repeat scroll 0 0;
    border: 1px solid #333;
    color: var(--black-color);
    font-size: 16px;
    padding: 8px 20px;
    display: inline-block;
    border-radius: 12px;
}

.owl-theme .owl-controls .owl-page span {
    display: block;
    width: 6px;
    height: 6px;
    margin: 5px 7px;
    filter: Alpha(Opacity=50);
    opacity: 0.5;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    border-radius: 20px;
    background: #000;
}

.single-blog {
    margin-right: 30px;
}

.single-blog h5 {
    font-weight: 300;
    font-size: 12px;
}

.single-blog h3 {
    color: #aeaeae;
    padding: 10px 15px;
    font-size: 10px;
    text-transform: uppercase;
    display: inline-block;
    letter-spacing: 3px;
}

.single-blog h2 {
    margin-top: 0;
}

.single-blog h2 a {
    font-size: 30px;
    margin-top: 0;
    margin-bottom: 20px;
    text-transform: capitalize;
    font-weight: 700;
    color: #000;
}

.single-blog p {
    padding-bottom: 30px;
}

.blog-description {
    padding: 20px 30px;
    border: 1px solid #f8f8f8;
    text-align: center;
}

.blog-area .owl-theme .owl-controls .owl-page span {
    display: block;
    width: 5px;
    height: 5px;
    margin: 5px 7px;
    border-radius: 20px;
    background: #000;
}

.single-blog-post-details,
.single-blog-sidebar-area,
.post-comments-area {
    padding-right: 60q;
}

.single-side-bar {}

.single-side-bar {
    -webkit-transition: 0.5s;
    transition: 0.5s;
    margin-bottom: 30px;
    border-radius: 14px;
    padding: 25px;
    border: 1px solid var(--border-color);
}

.custom-icon {
    display: flex; /* Use flexbox for alignment */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    height: 200px; /* Full height of the viewport */
}