/*!
 * ScrollSmoother 3.11.5
 * https://greensock.com
 * 
 * @license Copyright 2023, GreenSock. All rights reserved.
 * This plugin is a membership benefit of Club GreenSock and is only authorized for use in sites/apps/products developed by individuals/companies with an active Club GreenSock membership. See https://greensock.com/club
 * @author: <PERSON>, <EMAIL>
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(e){"use strict";function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(){return"undefined"!=typeof window}function t(){return D||s()&&(D=window.gsap)&&D.registerPlugin&&D}var D,L,O,N,U,J,K,q,V,j,Y,W,G,Q,X,r=(ScrollSmoother.register=function register(e){return L||(D=e||t(),s()&&window.document&&(O=window,N=document,U=N.documentElement,J=N.body),D&&(K=D.utils.toArray,q=D.utils.clamp,Y=D.parseEase("expo"),Q=D.core.context||function(){},X=D.delayedCall(.2,function(){return V.isRefreshing||j&&j.refresh()}).pause(),V=D.core.globals().ScrollTrigger,D.core.globals("ScrollSmoother",ScrollSmoother),J&&V&&(W=V.core._getVelocityProp,G=V.core._inputObserver,ScrollSmoother.refresh=V.refresh,L=1))),L},function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}(ScrollSmoother,[{key:"progress",get:function get(){return this.scrollTrigger?this.scrollTrigger.animation._time/100:0}}]),ScrollSmoother);function ScrollSmoother(t){var o=this;L||ScrollSmoother.register(D)||console.warn("Please gsap.registerPlugin(ScrollSmoother)"),t=this.vars=t||{},j&&j.kill(),Q(j=this);function ya(){return M.update(-H)}function Aa(){return n.style.overflow="visible"}function Ca(e){e.update();var t=e.getTween();t&&(t.pause(),t._time=t._dur,t._tTime=t._tDur),d=!1,e.animation.progress(e.progress,!0)}function Da(e,t){(e!==H&&!u||t)&&(x&&(e=Math.round(e)),k&&(n.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+e+", 0, 1)",n._gsap.y=e+"px"),A=e-H,H=e,V.isUpdating||ScrollSmoother.isRefreshing||V.update())}function Ea(e){return arguments.length?(e<0&&(e=0),I.y=-e,d=!0,u?H=-e:Da(-e),V.isRefreshing?i.update():R(e/_),this):-H}function Ha(e){S.scrollTop=0,e.target.contains&&e.target.contains(S)||T&&!1===T(o,e)||(V.isInViewport(e.target)||e.target===g||o.scrollTo(e.target,!1,"center center"),g=e.target)}function Ia(e){var r,n,o,i;b.forEach(function(t){r=t.pins,i=t.markers,e.forEach(function(e){t.trigger&&e.trigger&&t!==e&&(e.trigger===t.trigger||e.pinnedContainer===t.trigger||t.trigger.contains(e.trigger))&&(n=e.start,o=(n-t.start-t.offset)/t.ratio-(n-t.start),r.forEach(function(e){return o-=e.distance/t.ratio-e.distance}),e.setPositions(n+o,e.end+o),e.markerStart&&i.push(D.quickSetter([e.markerStart,e.markerEnd],"y","px")),e.pin&&0<e.end&&(o=e.end-e.start,r.push({start:e.start,end:e.end,distance:o,trig:e}),t.setPositions(t.start,t.end+o),t.vars.onRefresh(t)))})})}function Ja(){Aa(),requestAnimationFrame(Aa),b&&(b.forEach(function(e){var t=e.start,r=e.auto?Math.min(V.maxScroll(e.scroller),e.end):t+(e.end-t)/e.ratio,n=(r-e.end)/2;t-=n,r-=n,e.offset=n||1e-4,e.pins.length=0,e.setPositions(Math.min(t,r),Math.max(t,r)),e.vars.onRefresh(e)}),Ia(V.sort())),M.reset()}function Ka(){return V.addEventListener("refresh",Ja)}function La(){return b&&b.forEach(function(e){return e.vars.onRefresh(e)})}function Ma(){return b&&b.forEach(function(e){return e.vars.onRefreshInit(e)}),La}function Na(t,r,n,o){return function(){var e="function"==typeof r?r(n,o):r;return e||0===e||(e=o.getAttribute("data-"+P+t)||("speed"===t?1:0)),o.setAttribute("data-"+P+t,e),"auto"===e?e:parseFloat(e)}}function Oa(r,e,t,n,o){function Eb(){e=f(),t=h(),i=parseFloat(e)||1,c=(a="auto"===e)?0:.5,l&&l.kill(),l=t&&D.to(r,{ease:Y,overwrite:!1,y:"+=0",duration:t}),s&&(s.ratio=i,s.autoSpeed=a)}function Fb(){g.y=d+"px",g.renderTransform(1),Eb()}function Jb(e){if(a){Fb();var t=function _autoDistance(e,t){var r,n,o=e.parentNode||U,i=e.getBoundingClientRect(),s=o.getBoundingClientRect(),a=s.top-i.top,l=s.bottom-i.bottom,c=(Math.abs(a)>Math.abs(l)?a:l)/(1-t),u=-c*t;return 0<c&&(n=.5==(r=s.height/(O.innerHeight+s.height))?2*s.height:2*Math.min(s.height,-c*r/(2*r-1))*(t||1),u+=t?-n*t:-n/2,c+=n),{change:c,offset:u}}(r,q(0,1,-e.start/(e.end-e.start)));v=t.change,u=t.offset}else v=(e.end-e.start)*(1-i),u=0;p.forEach(function(e){return v-=e.distance*(1-i)}),e.vars.onUpdate(e),l&&l.progress(1)}o=("function"==typeof o?o(n,r):o)||0;var i,s,a,l,c,u,f=Na("speed",e,n,r),h=Na("lag",t,n,r),d=D.getProperty(r,"y"),g=r._gsap,p=[],m=[],v=0;return Eb(),(1!==i||a||l)&&(Jb(s=V.create({trigger:a?r.parentNode:r,start:"top bottom+="+o,end:"bottom top-="+o,scroller:S,scrub:!0,refreshPriority:-999,onRefreshInit:Fb,onRefresh:Jb,onKill:function onKill(e){var t=b.indexOf(e);0<=t&&b.splice(t,1),Fb()},onUpdate:function onUpdate(e){var t,r,n,o=d+v*(e.progress-c),i=p.length,s=0;if(e.offset){if(i){for(r=-H,n=e.end;i--;){if((t=p[i]).trig.isActive||r>=t.start&&r<=t.end)return void(l&&(t.trig.progress+=t.trig.direction<0?.001:-.001,t.trig.update(0,0,1),l.resetTo("y",parseFloat(g.y),-A,!0),F&&l.progress(1)));r>t.end&&(s+=t.distance),n-=t.distance}o=d+s+v*((D.utils.clamp(e.start,e.end,r)-e.start-s)/(n-e.start)-c)}o=function _round(e){return Math.round(1e5*e)/1e5||0}(o+u),m.length&&!a&&m.forEach(function(e){return e(o-s)}),l?(l.resetTo("y",o,-A,!0),F&&l.progress(1)):(g.y=o+"px",g.renderTransform(1))}}})),D.core.getCache(s.trigger).stRevert=Ma,s.startY=d,s.pins=p,s.markers=m,s.ratio=i,s.autoSpeed=a,r.style.willChange="transform"),s}var n,S,e,i,b,s,a,l,c,u,r,f,h,d,g,p=t.smoothTouch,m=t.onUpdate,v=t.onStop,w=t.smooth,T=t.onFocusIn,E=t.normalizeScroll,x=t.wholePixels,C=this,P=t.effectsPrefix||"",R=V.getScrollFunc(O),k=1===V.isTouch?!0===p?.8:parseFloat(p)||0:0===w||!1===w?0:parseFloat(w)||.8,_=k&&+t.speed||1,H=0,A=0,F=1,M=W(0),I={y:0},z="undefined"!=typeof ResizeObserver&&!1!==t.autoResize&&new ResizeObserver(function(){if(!V.isRefreshing){var e=V.maxScroll(S);e<-H&&Ea(e),X.restart(!0)}});function refreshHeight(){return e=n.clientHeight,n.style.overflow="visible",J.style.height=O.innerHeight+(e-O.innerHeight)/_+"px",e-O.innerHeight}Ka(),V.addEventListener("killAll",Ka),D.delayedCall(.5,function(){return F=0}),this.scrollTop=Ea,this.scrollTo=function(e,t,r){var n=D.utils.clamp(0,V.maxScroll(O),isNaN(e)?o.offset(e,r):+e);t?u?D.to(o,{duration:k,scrollTop:n,overwrite:"auto",ease:Y}):R(n):Ea(n)},this.offset=function(e,t){var r,n=(e=K(e)[0]).style.cssText,o=V.create({trigger:e,start:t||"top top"});return b&&Ia([o]),r=o.start/_,o.kill(!1),e.style.cssText=n,D.core.getCache(e).uncache=1,r},this.content=function(e){if(arguments.length){var t=K(e||"#smooth-content")[0]||console.warn("ScrollSmoother needs a valid content element.")||J.children[0];return t!==n&&(c=(n=t).getAttribute("style")||"",z&&z.observe(n),D.set(n,{overflow:"visible",width:"100%",boxSizing:"border-box",y:"+=0"}),k||D.set(n,{clearProps:"transform"})),this}return n},this.wrapper=function(e){return arguments.length?(S=K(e||"#smooth-wrapper")[0]||function _wrap(e){var t=N.querySelector(".ScrollSmoother-wrapper");return t||((t=N.createElement("div")).classList.add("ScrollSmoother-wrapper"),e.parentNode.insertBefore(t,e),t.appendChild(e)),t}(n),l=S.getAttribute("style")||"",refreshHeight(),D.set(S,k?{overflow:"hidden",position:"fixed",height:"100%",width:"100%",top:0,left:0,right:0,bottom:0}:{overflow:"visible",position:"relative",width:"100%",height:"auto",top:"auto",bottom:"auto",left:"auto",right:"auto"}),this):S},this.effects=function(e,t){if(b=b||[],!e)return b.slice(0);(e=K(e)).forEach(function(e){for(var t=b.length;t--;)b[t].trigger===e&&b[t].kill()});t=t||{};var r,n,o=t.speed,i=t.lag,s=t.effectsPadding,a=[];for(r=0;r<e.length;r++)(n=Oa(e[r],o,i,r,s))&&a.push(n);return b.push.apply(b,a),a},this.sections=function(e,t){if(s=s||[],!e)return s.slice(0);var r=K(e).map(function(t){return V.create({trigger:t,start:"top 120%",end:"bottom -20%",onToggle:function onToggle(e){t.style.opacity=e.isActive?"1":"0",t.style.pointerEvents=e.isActive?"all":"none"}})});return t&&t.add?s.push.apply(s,r):s=r.slice(0),r},this.content(t.content),this.wrapper(t.wrapper),this.render=function(e){return Da(e||0===e?e:H)},this.getVelocity=function(){return M.getVelocity(-H)},V.scrollerProxy(S,{scrollTop:Ea,scrollHeight:function scrollHeight(){return refreshHeight()&&J.scrollHeight},fixedMarkers:!1!==t.fixedMarkers&&!!k,content:n,getBoundingClientRect:function getBoundingClientRect(){return{top:0,left:0,width:O.innerWidth,height:O.innerHeight}}}),V.defaults({scroller:S});var B=V.getAll().filter(function(e){return e.scroller===O||e.scroller===S});B.forEach(function(e){return e.revert(!0,!0)}),i=V.create({animation:D.fromTo(I,{y:0},{y:function y(){return-refreshHeight()},immediateRender:!1,ease:"none",data:"ScrollSmoother",duration:100,onUpdate:function onUpdate(){if(this._dur){var e=d;e&&(Ca(i),I.y=H),Da(I.y,e),ya(),m&&!u&&m(C)}}}),onRefreshInit:function onRefreshInit(e){if(ScrollSmoother.isRefreshing=!0,b){var t=V.getAll().filter(function(e){return!!e.pin});b.forEach(function(r){r.vars.pinnedContainer||t.forEach(function(e){if(e.pin.contains(r.trigger)){var t=r.vars;t.pinnedContainer=e.pin,r.vars=null,r.init(t,r.animation)}})})}var r=e.getTween();h=r&&r._end>r._dp._time,f=H,I.y=0,k&&(1===V.isTouch&&(S.style.position="absolute"),S.scrollTop=0,1===V.isTouch&&(S.style.position="fixed"))},onRefresh:function onRefresh(e){e.animation.invalidate(),e.setPositions(e.start,refreshHeight()/_),h||Ca(e),I.y=-R()*_,Da(I.y),F||e.animation.progress(D.utils.clamp(0,1,f/_/-e.end)),h&&(e.progress-=.001,e.update()),ScrollSmoother.isRefreshing=!1},id:"ScrollSmoother",scroller:O,invalidateOnRefresh:!0,start:0,refreshPriority:-9999,end:function end(){return refreshHeight()/_},onScrubComplete:function onScrubComplete(){M.reset(),v&&v(o)},scrub:k||!0}),this.smooth=function(e){return arguments.length&&(_=(k=e||0)&&+t.speed||1,i.scrubDuration(e)),i.getTween()?i.getTween().duration():0},i.getTween()&&(i.getTween().vars.ease=t.ease||Y),this.scrollTrigger=i,t.effects&&this.effects(!0===t.effects?"[data-"+P+"speed], [data-"+P+"lag]":t.effects,{effectsPadding:t.effectsPadding}),t.sections&&this.sections(!0===t.sections?"[data-section]":t.sections),B.forEach(function(e){e.vars.scroller=S,e.revert(!1,!0),e.init(e.vars,e.animation)}),this.paused=function(e,t){return arguments.length?(!!u!==e&&(e?(i.getTween()&&i.getTween().pause(),R(-H/_),M.reset(),(r=V.normalizeScroll())&&r.disable(),(u=V.observe({preventDefault:!0,type:"wheel,touch,scroll",debounce:!1,allowClicks:!0,onChangeY:function onChangeY(){return Ea(-H)}})).nested=G(U,"wheel,touch,scroll",!0,!1!==t)):(u.nested.kill(),u.kill(),u=0,r&&r.enable(),i.progress=(-H/_-i.start)/(i.end-i.start),Ca(i))),this):!!u},this.kill=this.revert=function(){o.paused(!1),Ca(i),i.kill();for(var e=(b||[]).concat(s||[]),t=e.length;t--;)e[t].kill();V.scrollerProxy(S),V.removeEventListener("killAll",Ka),V.removeEventListener("refresh",Ja),S.style.cssText=l,n.style.cssText=c;var r=V.defaults({});r&&r.scroller===S&&V.defaults({scroller:O}),o.normalizer&&V.normalizeScroll(!1),clearInterval(a),j=null,z&&z.disconnect(),J.style.removeProperty("height"),O.removeEventListener("focusin",Ha)},this.refresh=function(e,t){return i.refresh(e,t)},E&&(this.normalizer=V.normalizeScroll(!0===E?{debounce:!0,content:!k&&n}:E)),V.config(t),"overscrollBehavior"in O.getComputedStyle(J)&&D.set([J,U],{overscrollBehavior:"none"}),"scrollBehavior"in O.getComputedStyle(J)&&D.set([J,U],{scrollBehavior:"auto"}),O.addEventListener("focusin",Ha),a=setInterval(ya,250),"loading"===N.readyState||requestAnimationFrame(function(){return V.refresh()})}r.version="3.11.5",r.create=function(e){return j&&e&&j.content()===K(e.content)[0]?j:new r(e)},r.get=function(){return j},t()&&D.registerPlugin(r),e.ScrollSmoother=r,e.default=r;if (typeof(window)==="undefined"||window!==e){Object.defineProperty(e,"__esModule",{value:!0})} else {delete e.default}});

