<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\ContactController;

// Home route
Route::get('/', [HomeController::class, 'index'])->name('home');

// About routes
Route::get('/about', [AboutController::class, 'index'])->name('about');
Route::get('/download-cv', [AboutController::class, 'downloadCV'])->name('download.cv');
Route::get('/api/skills', [AboutController::class, 'getSkills'])->name('api.skills');

// Projects routes
Route::get('/projects', [ProjectController::class, 'index'])->name('projects');
Route::get('/project/{portfolio}', [ProjectController::class, 'show'])->name('project.show');
Route::get('/single-project/{slug?}', [ProjectController::class, 'single'])->name('single-project');
Route::get('/slides-project', [ProjectController::class, 'slides'])->name('slides-project');
Route::get('/api/projects/category', [ProjectController::class, 'getByCategory'])->name('api.projects.category');

// Blog routes
Route::get('/blog', [BlogController::class, 'index'])->name('blog');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');
Route::get('/single-blog/{slug?}', [BlogController::class, 'single'])->name('single-blog');
Route::get('/api/blog/tag', [BlogController::class, 'getByTag'])->name('api.blog.tag');
Route::get('/api/blog/search', [BlogController::class, 'search'])->name('api.blog.search');

// Contact routes
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::get('/api/contact/stats', [ContactController::class, 'getStats'])->name('api.contact.stats');
Route::patch('/api/contact/{contact}/read', [ContactController::class, 'markAsRead'])->name('api.contact.read');
Route::patch('/api/contact/{contact}/replied', [ContactController::class, 'markAsReplied'])->name('api.contact.replied');

// Admin routes (simple demo - in production, add authentication middleware)
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [App\Http\Controllers\AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/portfolios', [App\Http\Controllers\AdminController::class, 'portfolios'])->name('portfolios');
    Route::get('/portfolios/create', [App\Http\Controllers\AdminController::class, 'createPortfolio'])->name('portfolios.create');
    Route::post('/portfolios', [App\Http\Controllers\AdminController::class, 'storePortfolio'])->name('portfolios.store');
    Route::get('/blogs', [App\Http\Controllers\AdminController::class, 'blogs'])->name('blogs');
    Route::get('/blogs/create', [App\Http\Controllers\AdminController::class, 'createBlog'])->name('blogs.create');
    Route::post('/blogs', [App\Http\Controllers\AdminController::class, 'storeBlog'])->name('blogs.store');
    Route::get('/contacts', [App\Http\Controllers\AdminController::class, 'contacts'])->name('contacts');
    Route::get('/api/stats', [App\Http\Controllers\AdminController::class, 'getStats'])->name('api.stats');
});

// Special routes
Route::get('/ilyass', function () {
    return view('ilyass');
})->name('ilyass');


