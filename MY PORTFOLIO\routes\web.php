<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\ContactController;

// Home route
Route::get('/', [HomeController::class, 'index'])->name('home');

// About route
Route::get('/about', [AboutController::class, 'index'])->name('about');

// Projects routes
Route::get('/projects', [ProjectController::class, 'index'])->name('projects');
Route::get('/single-project', [ProjectController::class, 'single'])->name('single-project');
Route::get('/slides-project', [ProjectController::class, 'slides'])->name('slides-project');

// Blog routes
Route::get('/blog', [BlogController::class, 'index'])->name('blog');
Route::get('/single-blog', [BlogController::class, 'single'])->name('single-blog');

// Contact routes
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// Special routes
Route::get('/ilyass', function () {
    return view('ilyass');
})->name('ilyass');


